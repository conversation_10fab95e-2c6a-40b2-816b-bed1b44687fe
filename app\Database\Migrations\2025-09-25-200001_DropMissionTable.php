<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class DropMissionTable extends Migration
{
    public function up()
    {
        // Drop the mission table completely as the mission feature is being removed
        if ($this->db->tableExists('mission')) {
            $this->forge->dropTable('mission');
        }
    }

    public function down()
    {
        // Recreate the mission table if needed (for rollback purposes)
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'mission_number' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'unique' => true,
            ],
            'mission_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'mission_date' => [
                'type' => 'DATE',
            ],
            'mission_status' => [
                'type' => 'ENUM',
                'constraint' => ['pending', 'in_progress', 'completed', 'cancelled'],
                'default' => 'pending',
            ],
            'user_id' => [
                'type' => 'BIGINT',
                'unsigned' => true,
                'null' => true,
            ],
            'commodity_id' => [
                'type' => 'BIGINT',
                'unsigned' => true,
                'null' => true,
            ],
            'location_id' => [
                'type' => 'BIGINT',
                'unsigned' => true,
                'null' => true,
            ],
            'budgeted_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '15,2',
                'null' => true,
            ],
            'actual_amount' => [
                'type' => 'DECIMAL',
                'constraint' => '15,2',
                'null' => true,
                'default' => 0.00,
            ],
            'remarks' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'created_by' => [
                'type' => 'INT',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_by' => [
                'type' => 'INT',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'deleted_by' => [
                'type' => 'INT',
                'null' => true,
            ],
            'is_deleted' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('mission_number');
        $this->forge->addKey('mission_name');
        $this->forge->addKey('mission_date');
        $this->forge->addKey('mission_status');
        $this->forge->addKey('user_id');
        $this->forge->addKey('commodity_id');
        $this->forge->addKey('location_id');
        $this->forge->addKey('budgeted_amount');
        $this->forge->addKey('created_at');
        $this->forge->addKey('deleted_at');
        $this->forge->addKey('is_deleted');

        $this->forge->createTable('mission');
    }
}
