<?php

namespace App\Models;

use CodeIgniter\Model;

class DistrictModel extends Model
{
    protected $table = 'districts';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'province_id',
        'name',
        'code'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = '';

    // Validation
    protected $validationRules = [
        'province_id' => 'required|integer|is_not_unique[provinces.id]',
        'name' => 'required|min_length[2]|max_length[100]',
        'code' => 'permit_empty|max_length[10]'
    ];

    protected $validationMessages = [
        'province_id' => [
            'required' => 'Province is required.',
            'integer' => 'Province must be a valid selection.',
            'is_not_unique' => 'Selected province does not exist.'
        ],
        'name' => [
            'required' => 'District name is required.',
            'min_length' => 'District name must be at least 2 characters long.',
            'max_length' => 'District name cannot exceed 100 characters.'
        ],
        'code' => [
            'max_length' => 'District code cannot exceed 10 characters.'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get districts by province for dropdown
     */
    public function getByProvinceForDropdown(int $provinceId): array
    {
        return $this->select('id, name')
                    ->where('province_id', $provinceId)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get all districts for dropdown with hierarchy
     */
    public function getForDropdown(): array
    {
        return $this->select('districts.id, districts.name, provinces.name as province_name, countries.name as country_name')
                    ->join('provinces', 'provinces.id = districts.province_id')
                    ->join('countries', 'countries.id = provinces.country_id')
                    ->orderBy('countries.name, provinces.name, districts.name', 'ASC')
                    ->findAll();
    }

    /**
     * Get districts with hierarchy information
     */
    public function getDistrictsWithHierarchy(int $perPage = 20, string $search = '', int $provinceId = null): array
    {
        $builder = $this->select('districts.*, provinces.name as province_name, countries.name as country_name')
                        ->join('provinces', 'provinces.id = districts.province_id')
                        ->join('countries', 'countries.id = provinces.country_id');
        
        if (!empty($search)) {
            $builder->groupStart()
                    ->like('districts.name', $search)
                    ->orLike('districts.code', $search)
                    ->orLike('provinces.name', $search)
                    ->orLike('countries.name', $search)
                    ->groupEnd();
        }
        
        if ($provinceId) {
            $builder->where('districts.province_id', $provinceId);
        }
        
        return $builder->orderBy('countries.name, provinces.name, districts.name', 'ASC')
                      ->paginate($perPage);
    }

    /**
     * Get district with hierarchy details
     */
    public function getDistrictWithHierarchy(int $id): ?array
    {
        return $this->select('districts.*, provinces.name as province_name, provinces.country_id, countries.name as country_name, countries.code as country_code')
                    ->join('provinces', 'provinces.id = districts.province_id')
                    ->join('countries', 'countries.id = provinces.country_id')
                    ->where('districts.id', $id)
                    ->first();
    }

    /**
     * Create a new district
     */
    public function createDistrict(array $data): bool
    {
        return $this->insert($data) !== false;
    }

    /**
     * Update district data
     */
    public function updateDistrict(int $id, array $data): bool
    {
        return $this->update($id, $data);
    }

    /**
     * Check if district has LLGs
     */
    public function hasLlgs(int $districtId): bool
    {
        $llgModel = new \App\Models\LlgModel();
        return $llgModel->where('district_id', $districtId)->countAllResults() > 0;
    }

    /**
     * Get district statistics
     */
    public function getDistrictStats(): array
    {
        return [
            'total_districts' => $this->countAllResults(),
            'recent_districts' => $this->where('created_at >=', date('Y-m-d H:i:s', strtotime('-30 days')))
                                      ->countAllResults()
        ];
    }
}
