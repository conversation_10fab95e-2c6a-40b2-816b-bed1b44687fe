-- Define the unit_of_measurement ENUM type
CREATE TYPE unit_of_measurement AS ENUM ('kg', 'g', 'lb', 'ton', 'liter', 'ml', 'gallon', 'piece', 'unit', 'box', 'carton', 'pack');

-- Create the commodities table
CREATE TABLE commodities (
    commodity_id SERIAL PRIMARY KEY,
    commodity_name VARCHAR(255) NOT NULL,
    unit_of_measurement unit_of_measurement NOT NULL,
    remarks TEXT,

    -- Audit fields
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by INT NOT NULL,

    -- Soft delete fields
    deleted_at TIMESTAMP,
    deleted_by INT,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE
);