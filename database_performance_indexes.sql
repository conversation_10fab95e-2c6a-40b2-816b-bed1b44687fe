-- =====================================================
-- Database Performance Optimization Indexes
-- =====================================================
-- Run these SQL commands in your Supabase SQL editor
-- to improve query performance for the DCBuyer application
-- =====================================================

-- Mission table indexes
-- These indexes will speed up mission queries significantly
CREATE INDEX IF NOT EXISTS idx_mission_status ON mission(mission_status);
CREATE INDEX IF NOT EXISTS idx_mission_user_id ON mission(user_id);
CREATE INDEX IF NOT EXISTS idx_mission_commodity_id ON mission(commodity_id);
CREATE INDEX IF NOT EXISTS idx_mission_location_id ON mission(location_id);
CREATE INDEX IF NOT EXISTS idx_mission_created_at ON mission(created_at);
CREATE INDEX IF NOT EXISTS idx_mission_mission_date ON mission(mission_date);
CREATE INDEX IF NOT EXISTS idx_mission_deleted_at ON mission(deleted_at);

-- Composite index for common mission queries
CREATE INDEX IF NOT EXISTS idx_mission_status_deleted ON mission(mission_status, deleted_at);
CREATE INDEX IF NOT EXISTS idx_mission_user_status ON mission(user_id, mission_status);

-- Users table indexes
-- These will speed up user lookups and authentication
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_user_type ON users(user_type);
CREATE INDEX IF NOT EXISTS idx_users_reports_to ON users(reports_to);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Locations table indexes
-- These will speed up location hierarchy queries
CREATE INDEX IF NOT EXISTS idx_locations_country_id ON locations(country_id);
CREATE INDEX IF NOT EXISTS idx_locations_province_id ON locations(province_id);
CREATE INDEX IF NOT EXISTS idx_locations_district_id ON locations(district_id);
CREATE INDEX IF NOT EXISTS idx_locations_llg_id ON locations(llg_id);
CREATE INDEX IF NOT EXISTS idx_locations_is_deleted ON locations(is_deleted);

-- Composite index for location hierarchy
CREATE INDEX IF NOT EXISTS idx_locations_hierarchy ON locations(country_id, province_id, district_id, llg_id);

-- LLGs table indexes
-- These will speed up LLG hierarchy queries
CREATE INDEX IF NOT EXISTS idx_llgs_district_id ON llgs(district_id);
CREATE INDEX IF NOT EXISTS idx_llgs_name ON llgs(name);
CREATE INDEX IF NOT EXISTS idx_llgs_code ON llgs(code);

-- Districts table indexes
CREATE INDEX IF NOT EXISTS idx_districts_province_id ON districts(province_id);
CREATE INDEX IF NOT EXISTS idx_districts_name ON districts(name);

-- Provinces table indexes
CREATE INDEX IF NOT EXISTS idx_provinces_country_id ON provinces(country_id);
CREATE INDEX IF NOT EXISTS idx_provinces_name ON provinces(name);

-- Countries table indexes
CREATE INDEX IF NOT EXISTS idx_countries_name ON countries(name);
CREATE INDEX IF NOT EXISTS idx_countries_code ON countries(code);

-- Commodities table indexes
-- These will speed up commodity lookups
CREATE INDEX IF NOT EXISTS idx_commodities_commodity_name ON commodities(commodity_name);
CREATE INDEX IF NOT EXISTS idx_commodities_category ON commodities(category);
CREATE INDEX IF NOT EXISTS idx_commodities_status ON commodities(status);

-- Orders table indexes (if exists)
-- Uncomment these if you have an orders table
-- CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
-- CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(order_status);
-- CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);

-- Transactions table indexes (if exists)
-- Uncomment these if you have a transactions table
-- CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
-- CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(transaction_status);
-- CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);

-- =====================================================
-- Performance Analysis Queries
-- =====================================================
-- Use these queries to analyze performance after creating indexes

-- Check index usage
-- SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch 
-- FROM pg_stat_user_indexes 
-- ORDER BY idx_scan DESC;

-- Check table sizes
-- SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
-- FROM pg_tables 
-- WHERE schemaname = 'public'
-- ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- =====================================================
-- Notes:
-- =====================================================
-- 1. Run these commands during low-traffic periods
-- 2. Monitor query performance before and after
-- 3. Some indexes may already exist - IF NOT EXISTS prevents errors
-- 4. Consider your specific query patterns when adding more indexes
-- 5. Too many indexes can slow down INSERT/UPDATE operations
-- 6. Monitor index usage with pg_stat_user_indexes
-- =====================================================
