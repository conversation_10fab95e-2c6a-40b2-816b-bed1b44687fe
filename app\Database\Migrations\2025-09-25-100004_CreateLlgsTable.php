<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateLlgsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'SERIAL',
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'district_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'code' => [
                'type' => 'VARCHAR',
                'constraint' => 10,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('district_id');
        $this->forge->createTable('llgs');

        // Set default timestamp
        $this->db->query('ALTER TABLE llgs ALTER COLUMN created_at SET DEFAULT CURRENT_TIMESTAMP');

        // Add indexes for performance
        $this->db->query('CREATE INDEX idx_llgs_district_id ON llgs(district_id)');
        $this->db->query('CREATE INDEX idx_llgs_name ON llgs(name)');

        // Add foreign key constraint
        if ($this->db->DBDriver === 'Postgre') {
            $this->db->query('ALTER TABLE llgs ADD CONSTRAINT fk_llgs_district FOREIGN KEY (district_id) REFERENCES districts(id) ON DELETE CASCADE');
        }
    }

    public function down()
    {
        // Drop foreign key constraint first
        if ($this->db->DBDriver === 'Postgre') {
            $this->db->query('ALTER TABLE llgs DROP CONSTRAINT IF EXISTS fk_llgs_district');
        }
        
        $this->forge->dropTable('llgs');
    }
}
