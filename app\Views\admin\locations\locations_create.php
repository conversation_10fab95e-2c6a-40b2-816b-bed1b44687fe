<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .form-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .form-section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #2E7D32;
        box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
    }
    
    .required {
        color: #dc3545;
    }
    
    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .hierarchy-info {
        background-color: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .hierarchy-info h6 {
        color: #1976d2;
        margin-bottom: 0.5rem;
    }
    
    .hierarchy-path {
        font-size: 0.9rem;
        color: #424242;
    }
    
    .gps-input-group {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
    
    .loading-spinner {
        display: none;
        width: 1rem;
        height: 1rem;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-left: 0.5rem;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Display Validation Errors -->
    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please correct the following errors:</h6>
            <ul class="mb-0">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Display Error Message -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <form action="<?= base_url('admin/locations') ?>" method="POST" id="locationForm">
        <?= csrf_field() ?>
        
        <!-- Location Hierarchy Section -->
        <div class="form-section">
            <div class="form-section-title">
                <i class="fas fa-sitemap me-2"></i>Location Hierarchy
            </div>
            
            <div class="hierarchy-info" id="hierarchyInfo" style="display: none;">
                <h6><i class="fas fa-map-marked-alt me-2"></i>Selected Location Path</h6>
                <div class="hierarchy-path" id="hierarchyPath">Please select location hierarchy...</div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="country_id" class="form-label">Country <span class="required">*</span></label>
                        <select class="form-select" id="country_id" name="country_id" required>
                            <option value="">Select Country</option>
                            <?php foreach ($countries as $country): ?>
                                <option value="<?= $country['id'] ?>" <?= old('country_id') == $country['id'] ? 'selected' : '' ?>>
                                    <?= esc($country['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Select the country for this location.</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="province_id" class="form-label">
                            Province <span class="required">*</span>
                            <span class="loading-spinner" id="provinceSpinner"></span>
                        </label>
                        <select class="form-select" id="province_id" name="province_id" required disabled>
                            <option value="">Select Province</option>
                        </select>
                        <div class="form-text">Select the province within the chosen country.</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="district_id" class="form-label">
                            District <span class="required">*</span>
                            <span class="loading-spinner" id="districtSpinner"></span>
                        </label>
                        <select class="form-select" id="district_id" name="district_id" required disabled>
                            <option value="">Select District</option>
                        </select>
                        <div class="form-text">Select the district within the chosen province.</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="llg_id" class="form-label">
                            LLG (Local Level Government) <span class="required">*</span>
                            <span class="loading-spinner" id="llgSpinner"></span>
                        </label>
                        <select class="form-select" id="llg_id" name="llg_id" required disabled>
                            <option value="">Select LLG</option>
                        </select>
                        <div class="form-text">Select the Local Level Government within the chosen district.</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Location Details Section -->
        <div class="form-section">
            <div class="form-section-title">
                <i class="fas fa-map-marker-alt me-2"></i>Location Details
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="ward" class="form-label">Ward <span class="required">*</span></label>
                        <input type="text" class="form-control" id="ward" name="ward" 
                               value="<?= old('ward') ?>" required maxlength="100">
                        <div class="form-text">Enter the ward name for this location.</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="location_name" class="form-label">Location Name <span class="required">*</span></label>
                        <input type="text" class="form-control" id="location_name" name="location_name" 
                               value="<?= old('location_name') ?>" required maxlength="100">
                        <div class="form-text">Enter the specific location name.</div>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">GPS Coordinates (Optional)</label>
                <div class="gps-input-group">
                    <div>
                        <label for="gps_latitude" class="form-label">Latitude</label>
                        <input type="number" class="form-control" id="gps_latitude" name="gps_latitude" 
                               value="<?= old('gps_latitude') ?>" step="0.000001" min="-90" max="90" 
                               placeholder="e.g., -6.314993">
                        <div class="form-text">Latitude in decimal degrees (-90 to 90)</div>
                    </div>
                    <div>
                        <label for="gps_longitude" class="form-label">Longitude</label>
                        <input type="number" class="form-control" id="gps_longitude" name="gps_longitude" 
                               value="<?= old('gps_longitude') ?>" step="0.000001" min="-180" max="180" 
                               placeholder="e.g., 143.95555">
                        <div class="form-text">Longitude in decimal degrees (-180 to 180)</div>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="remarks" class="form-label">Remarks (Optional)</label>
                <textarea class="form-control" id="remarks" name="remarks" rows="3" 
                          maxlength="1000"><?= old('remarks') ?></textarea>
                <div class="form-text">Additional notes or description about this location (max 1000 characters).</div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="form-section">
            <div class="d-flex justify-content-between">
                <a href="<?= base_url('admin/locations') ?>" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
                <button type="submit" class="btn btn-success" id="submitBtn">
                    <i class="fas fa-save me-2"></i>Create Location
                </button>
            </div>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const countrySelect = document.getElementById('country_id');
    const provinceSelect = document.getElementById('province_id');
    const districtSelect = document.getElementById('district_id');
    const llgSelect = document.getElementById('llg_id');
    const hierarchyInfo = document.getElementById('hierarchyInfo');
    const hierarchyPath = document.getElementById('hierarchyPath');

    // Cascading dropdown functionality
    countrySelect.addEventListener('change', function() {
        const countryId = this.value;
        resetDropdowns(['province', 'district', 'llg']);
        
        if (countryId) {
            loadProvinces(countryId);
        }
        updateHierarchyPath();
    });

    provinceSelect.addEventListener('change', function() {
        const provinceId = this.value;
        resetDropdowns(['district', 'llg']);
        
        if (provinceId) {
            loadDistricts(provinceId);
        }
        updateHierarchyPath();
    });

    districtSelect.addEventListener('change', function() {
        const districtId = this.value;
        resetDropdowns(['llg']);
        
        if (districtId) {
            loadLlgs(districtId);
        }
        updateHierarchyPath();
    });

    llgSelect.addEventListener('change', function() {
        updateHierarchyPath();
    });

    function resetDropdowns(types) {
        types.forEach(type => {
            const select = document.getElementById(type + '_id');
            select.innerHTML = `<option value="">Select ${type.charAt(0).toUpperCase() + type.slice(1)}</option>`;
            select.disabled = true;
        });
    }

    function showSpinner(type) {
        document.getElementById(type + 'Spinner').style.display = 'inline-block';
    }

    function hideSpinner(type) {
        document.getElementById(type + 'Spinner').style.display = 'none';
    }

    function loadProvinces(countryId) {
        showSpinner('province');
        fetch(`<?= base_url('admin/locations/api/provinces') ?>/${countryId}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                hideSpinner('province');
                if (data.success) {
                    data.data.forEach(province => {
                        provinceSelect.add(new Option(province.name, province.id));
                    });
                    provinceSelect.disabled = false;
                } else {
                    alert('Error loading provinces: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                hideSpinner('province');
                console.error('Error:', error);
                alert('Error loading provinces. Please try again.');
            });
    }

    function loadDistricts(provinceId) {
        showSpinner('district');
        fetch(`<?= base_url('admin/locations/api/districts') ?>/${provinceId}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                hideSpinner('district');
                if (data.success) {
                    data.data.forEach(district => {
                        districtSelect.add(new Option(district.name, district.id));
                    });
                    districtSelect.disabled = false;
                } else {
                    alert('Error loading districts: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                hideSpinner('district');
                console.error('Error:', error);
                alert('Error loading districts. Please try again.');
            });
    }

    function loadLlgs(districtId) {
        showSpinner('llg');
        fetch(`<?= base_url('admin/locations/api/llgs') ?>/${districtId}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                hideSpinner('llg');
                if (data.success) {
                    data.data.forEach(llg => {
                        llgSelect.add(new Option(llg.name, llg.id));
                    });
                    llgSelect.disabled = false;
                } else {
                    alert('Error loading LLGs: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                hideSpinner('llg');
                console.error('Error:', error);
                alert('Error loading LLGs. Please try again.');
            });
    }

    function updateHierarchyPath() {
        const country = countrySelect.options[countrySelect.selectedIndex]?.text || '';
        const province = provinceSelect.options[provinceSelect.selectedIndex]?.text || '';
        const district = districtSelect.options[districtSelect.selectedIndex]?.text || '';
        const llg = llgSelect.options[llgSelect.selectedIndex]?.text || '';

        const path = [country, province, district, llg].filter(item => item && !item.startsWith('Select')).join(' → ');
        
        if (path) {
            hierarchyPath.textContent = path;
            hierarchyInfo.style.display = 'block';
        } else {
            hierarchyInfo.style.display = 'none';
        }
    }

    // Form validation
    document.getElementById('locationForm').addEventListener('submit', function(e) {
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
    });
});
</script>
<?= $this->endSection() ?>
