<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UserModel;

class UsersController extends BaseController
{
    protected $userModel;
    protected $data = [];

    public function __construct()
    {
        $this->userModel = new UserModel();
        
        // Set common data for all methods
        $this->data = [
            'user_email' => session()->get('email'),
            'user_name' => session()->get('fullname') ?: session()->get('username') ?: 'User',
            'user_role' => session()->get('is_admin') ? 'administrator' : (session()->get('is_supervisor') ? 'supervisor' : 'user')
        ];
    }

    /**
     * Display users list
     */
    public function index()
    {
        // Get all users with error handling
        try {
            $this->data['users'] = $this->userModel->getUsersWithSupervisor(50);
            $this->data['total_users'] = $this->userModel->countAllResults();
        } catch (\Exception $e) {
            $this->data['users'] = [];
            $this->data['total_users'] = 0;
            log_message('error', 'Error fetching users: ' . $e->getMessage());
        }

        $this->data['title'] = 'Users Management - DCBuyer Admin';
        $this->data['active_menu'] = 'users';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Users', 'url' => base_url('admin/users')]
        ];
        
        $this->data['page_title'] = 'Users Management';
        $this->data['page_description'] = 'Manage platform users including farmers, buyers, and administrators.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/users/create') . '" class="btn btn-admin-primary">
                <i class="fas fa-user-plus me-2"></i>Add New User
            </a>
        ';
        
        return view('admin/users_list', $this->data);
    }

    /**
     * Show create user form (GET)
     */
    public function create()
    {
        $this->data['title'] = 'Create User - DCBuyer Admin';
        $this->data['active_menu'] = 'users';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Users', 'url' => base_url('admin/users')],
            ['title' => 'Create User', 'url' => base_url('admin/users/create')]
        ];

        $this->data['page_title'] = 'Create New User';
        $this->data['page_description'] = 'Add a new user to the platform.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/users') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Users
            </a>
        ';

        // Load supervisors for the dropdown
        try {
            $this->data['supervisors'] = $this->userModel->getSupervisorUsers();
        } catch (\Exception $e) {
            $this->data['supervisors'] = [];
            log_message('error', 'Error loading supervisors: ' . $e->getMessage());
        }

        return view('admin/users/create', $this->data);
    }

    /**
     * Store new user (POST)
     */
    public function store()
    {
        $rules = [
            'fullname' => 'required|min_length[2]|max_length[255]',
            'username' => 'required|min_length[3]|max_length[50]|is_unique[users.username]',
            'email' => 'required|valid_email|is_unique[users.email]',
            'password' => 'required|min_length[4]',
            'confirm_password' => 'required|matches[password]',
            'status' => 'required|in_list[active,inactive,suspended,pending]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Check if at least one role is selected
        $hasRole = $this->request->getPost('is_admin') || 
                   $this->request->getPost('is_supervisor') || 
                   $this->request->getPost('is_buyer');
        
        if (!$hasRole) {
            return redirect()->back()->withInput()->with('error', 'Please select at least one role for the user.');
        }
        
        $data = [
            'fullname' => $this->request->getPost('fullname'),
            'username' => $this->request->getPost('username'),
            'email' => $this->request->getPost('email'),
            'password' => $this->request->getPost('password'),
            'is_admin' => $this->request->getPost('is_admin') ? true : false,
            'is_buyer' => $this->request->getPost('is_buyer') ? true : false,
            'is_supervisor' => $this->request->getPost('is_supervisor') ? true : false,
            'reports_to' => $this->request->getPost('reports_to') ?: null,
            'status' => $this->request->getPost('status'),
            'remarks' => $this->request->getPost('remarks')
        ];
        
        try {
            $result = $this->userModel->createUser($data, session()->get('user_id'));
            
            if ($result) {
                return redirect()->to('admin/users')->with('success', 'User created successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to create user.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating user: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while creating the user.');
        }
    }

    /**
     * Show edit user form (GET)
     */
    public function edit($id)
    {
        $user = $this->userModel->find($id);

        if (!$user) {
            return redirect()->to('admin/users')->with('error', 'User not found.');
        }

        $this->data['user'] = $user;
        $this->data['title'] = 'Edit User - DCBuyer Admin';
        $this->data['active_menu'] = 'users';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Users', 'url' => base_url('admin/users')],
            ['title' => 'Edit User', 'url' => base_url('admin/users/edit/' . $id)]
        ];

        $this->data['page_title'] = 'Edit User: ' . $user['fullname'];
        $this->data['page_description'] = 'Update user information and permissions.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/users') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Users
            </a>
        ';

        // Load supervisors for the dropdown (exclude current user to prevent self-reporting)
        try {
            $supervisors = $this->userModel->getSupervisorUsers();
            // Remove current user from supervisors list to prevent self-reporting
            $this->data['supervisors'] = array_filter($supervisors, function($supervisor) use ($id) {
                return $supervisor['id'] != $id;
            });
        } catch (\Exception $e) {
            $this->data['supervisors'] = [];
            log_message('error', 'Error loading supervisors: ' . $e->getMessage());
        }

        return view('admin/users/edit', $this->data);
    }

    /**
     * Update user (PUT/PATCH)
     */
    public function update($id)
    {
        // Debug: Log the incoming request
        log_message('debug', 'Update method called for user ID: ' . $id);
        log_message('debug', 'POST data: ' . json_encode($this->request->getPost()));

        $user = $this->userModel->find($id);

        if (!$user) {
            log_message('error', 'User not found with ID: ' . $id);
            return redirect()->to('admin/users')->with('error', 'User not found.');
        }
        
        $rules = [
            'fullname' => 'required|min_length[2]|max_length[255]',
            'username' => "required|min_length[3]|max_length[50]|is_unique[users.username,id,{$id}]",
            'email' => "required|valid_email|is_unique[users.email,id,{$id}]",
            'status' => 'required|in_list[active,inactive,suspended,pending]'
        ];
        
        // Only validate password if provided
        if ($this->request->getPost('password')) {
            $rules['password'] = 'min_length[4]';
            $rules['confirm_password'] = 'matches[password]';
        }
        
        if (!$this->validate($rules)) {
            log_message('error', 'Validation failed: ' . json_encode($this->validator->getErrors()));
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        log_message('debug', 'Validation passed successfully');
        
        // Check if at least one role is selected
        $hasRole = $this->request->getPost('is_admin') || 
                   $this->request->getPost('is_supervisor') || 
                   $this->request->getPost('is_buyer');
        
        if (!$hasRole) {
            return redirect()->back()->withInput()->with('error', 'Please select at least one role for the user.');
        }
        
        $data = [
            'fullname' => $this->request->getPost('fullname'),
            'username' => $this->request->getPost('username'),
            'email' => $this->request->getPost('email'),
            'is_admin' => $this->request->getPost('is_admin') ? true : false,
            'is_buyer' => $this->request->getPost('is_buyer') ? true : false,
            'is_supervisor' => $this->request->getPost('is_supervisor') ? true : false,
            'reports_to' => $this->request->getPost('reports_to') ?: null,
            'status' => $this->request->getPost('status'),
            'remarks' => $this->request->getPost('remarks')
        ];
        
        // Only update password if provided
        if ($this->request->getPost('password')) {
            $data['password'] = $this->request->getPost('password');
        }
        
        try {
            // Log the data being sent for debugging
            log_message('debug', 'Updating user ID: ' . $id . ' with data: ' . json_encode($data));

            $result = $this->userModel->updateUser($id, $data, 1); // Use user ID 1 for testing

            if ($result) {
                return redirect()->to('admin/users')->with('success', 'User updated successfully.');
            } else {
                // Get any model errors
                $errors = $this->userModel->errors();
                log_message('error', 'User update failed. Model errors: ' . json_encode($errors));
                return redirect()->back()->withInput()->with('error', 'Failed to update user. ' . implode(', ', $errors));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error updating user: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating the user: ' . $e->getMessage());
        }
    }

    /**
     * View user details (GET)
     */
    public function show($id)
    {
        $user = $this->userModel->find($id);

        if (!$user) {
            return redirect()->to('admin/users')->with('error', 'User not found.');
        }

        $this->data['user'] = $user;
        $this->data['title'] = 'User Details - DCBuyer Admin';
        $this->data['active_menu'] = 'users';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Users', 'url' => base_url('admin/users')],
            ['title' => 'User Details', 'url' => base_url('admin/users/' . $id)]
        ];
        
        $this->data['page_title'] = 'User Details: ' . $user['fullname'];
        $this->data['page_description'] = 'View detailed information about this user.';
        
        return view('admin/user_details', $this->data);
    }

    /**
     * Delete user (DELETE) - Standard form submission
     */
    public function delete($id)
    {
        $user = $this->userModel->find($id);

        if (!$user) {
            return redirect()->to('admin/users')->with('error', 'User not found.');
        }

        // Prevent deleting current user
        if ($id == session()->get('user_id')) {
            return redirect()->to('admin/users')->with('error', 'You cannot delete your own account.');
        }

        try {
            $result = $this->userModel->deleteUser($id, session()->get('user_id'));

            if ($result) {
                return redirect()->to('admin/users')->with('success', 'User deleted successfully.');
            } else {
                return redirect()->to('admin/users')->with('error', 'Failed to delete user.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error deleting user: ' . $e->getMessage());
            return redirect()->to('admin/users')->with('error', 'An error occurred while deleting the user.');
        }
    }

    /**
     * Bulk operations - Standard form submission
     */
    public function bulkAction()
    {
        $action = $this->request->getPost('action');
        $userIds = $this->request->getPost('user_ids');

        if (!$action || !$userIds || !is_array($userIds)) {
            return redirect()->to('admin/users')->with('error', 'Invalid request. Please select users and an action.');
        }

        // Remove current user from bulk operations
        $currentUserId = session()->get('user_id');
        $userIds = array_filter($userIds, function($id) use ($currentUserId) {
            return $id != $currentUserId;
        });

        if (empty($userIds)) {
            return redirect()->to('admin/users')->with('error', 'No valid users selected. You cannot perform bulk operations on your own account.');
        }

        try {
            $count = 0;
            $actionName = '';

            switch ($action) {
                case 'activate':
                    $count = $this->userModel->bulkUpdateStatus($userIds, 'active', $currentUserId);
                    $actionName = 'activated';
                    break;
                case 'deactivate':
                    $count = $this->userModel->bulkUpdateStatus($userIds, 'inactive', $currentUserId);
                    $actionName = 'deactivated';
                    break;
                case 'delete':
                    $count = $this->userModel->bulkDelete($userIds, $currentUserId);
                    $actionName = 'deleted';
                    break;
                default:
                    return redirect()->to('admin/users')->with('error', 'Invalid action selected.');
            }

            $message = "Successfully {$actionName} {$count} user(s).";
            return redirect()->to('admin/users')->with('success', $message);

        } catch (\Exception $e) {
            log_message('error', 'Error in bulk operation: ' . $e->getMessage());
            return redirect()->to('admin/users')->with('error', 'An error occurred during the bulk operation.');
        }
    }

    /**
     * Export users - Standard form submission
     */
    public function export()
    {
        try {
            // Get filters from POST data
            $search = $this->request->getPost('search') ?? '';
            $roleFilter = $this->request->getPost('role_filter') ?? '';
            $statusFilter = $this->request->getPost('status_filter') ?? '';

            // Get filtered users
            $users = $this->userModel->getFilteredUsers($search, $roleFilter, $statusFilter);

            // Generate CSV content
            $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';

            // Set headers for file download
            $this->response->setHeader('Content-Type', 'text/csv');
            $this->response->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"');

            // Create CSV content
            $output = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($output, [
                'ID', 'Full Name', 'Username', 'Email', 'Status',
                'Is Admin', 'Is Supervisor', 'Is Buyer', 'Created At', 'Updated At'
            ]);

            // Add user data
            foreach ($users as $user) {
                fputcsv($output, [
                    $user['id'],
                    $user['fullname'],
                    $user['username'],
                    $user['email'],
                    $user['status'],
                    $user['is_admin'] ? 'Yes' : 'No',
                    $user['is_supervisor'] ? 'Yes' : 'No',
                    $user['is_buyer'] ? 'Yes' : 'No',
                    $user['created_at'],
                    $user['updated_at']
                ]);
            }

            fclose($output);
            return $this->response;

        } catch (\Exception $e) {
            log_message('error', 'Error exporting users: ' . $e->getMessage());
            return redirect()->to('admin/users')->with('error', 'An error occurred while exporting users.');
        }
    }
}
