<?php

namespace App\Controllers;

use App\Models\UserModel;

class Setup extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    /**
     * Create initial admin user and test data
     * This should only be run once during setup
     */
    public function createInitialData()
    {
        // Check if we're in development environment
        if (ENVIRONMENT !== 'development') {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'This endpoint is only available in development environment'
            ]);
        }

        try {
            // Create admin user
            $adminData = [
                'fullname' => 'System Administrator',
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'is_admin' => true,
                'is_buyer' => false,
                'is_supervisor' => true,
                'status' => 'active'
            ];

            // Check if admin already exists
            if (!$this->userModel->emailExists($adminData['email'])) {
                $result = $this->userModel->createUser($adminData);

                if ($result) {
                    $message[] = 'Admin user created successfully';
                } else {
                    $message[] = 'Failed to create admin user';
                }
            } else {
                $message[] = 'Admin user already exists';
            }

            // Create test buyer
            $buyerData = [
                'fullname' => 'John Buyer',
                'username' => 'johnbuyer',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'is_admin' => false,
                'is_buyer' => true,
                'is_supervisor' => false,
                'status' => 'active'
            ];

            if (!$this->userModel->emailExists($buyerData['email'])) {
                $result = $this->userModel->createUser($buyerData);

                if ($result) {
                    $message[] = 'Buyer user created successfully';
                } else {
                    $message[] = 'Failed to create buyer user';
                }
            } else {
                $message[] = 'Buyer user already exists';
            }

            // Create test supervisor
            $supervisorData = [
                'fullname' => 'Jane Supervisor',
                'username' => 'janesupervisor',
                'email' => '<EMAIL>',
                'password' => 'password123',
                'is_admin' => false,
                'is_buyer' => true,
                'is_supervisor' => true,
                'status' => 'active'
            ];

            if (!$this->userModel->emailExists($supervisorData['email'])) {
                $result = $this->userModel->createUser($supervisorData);

                if ($result) {
                    $message[] = 'Supervisor user created successfully';
                } else {
                    $message[] = 'Failed to create supervisor user';
                }
            } else {
                $message[] = 'Supervisor user already exists';
            }

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Setup completed',
                'details' => $message ?? []
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Setup error: ' . $e->getMessage());
            
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Setup failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test database connection
     */
    public function testConnection()
    {
        try {
            // Get database configuration
            $config = config('Database');
            $dbConfig = $config->default;

            // Test basic database connection
            $db = \Config\Database::connect();

            // Get connection details for debugging
            $connectionInfo = [
                'hostname' => $dbConfig['hostname'],
                'username' => $dbConfig['username'],
                'database' => $dbConfig['database'],
                'port' => $dbConfig['port'],
                'driver' => $dbConfig['DBDriver']
            ];

            // Try a simple query to test the connection
            $query = $db->query("SELECT 1 as test");
            $result = $query->getRow();

            if (!$result || $result->test != 1) {
                throw new \Exception('Database query test failed - got: ' . json_encode($result));
            }

            // Test if users table exists
            $tableExists = false;
            $tableError = '';
            try {
                $query = $db->query("SELECT COUNT(*) as count FROM users");
                $result = $query->getRow();
                $tableExists = true;
                $userCount = $result->count ?? 0;
            } catch (\Exception $e) {
                $tableExists = false;
                $tableError = $e->getMessage();
                $userCount = 0;
            }

            // Try to get users using the model (this will test the connection and table structure)
            $users = [];
            $userStats = [];
            $modelError = '';

            if ($tableExists) {
                try {
                    $users = $this->userModel->getAllUsers(5, 0);
                    $userStats = $this->userModel->getUserStats();
                } catch (\Exception $e) {
                    $modelError = $e->getMessage();
                    log_message('warning', 'Could not fetch users via model: ' . $e->getMessage());
                }
            }

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Database connection successful',
                'data' => [
                    'database_connected' => true,
                    'connection_info' => $connectionInfo,
                    'users_table_exists' => $tableExists,
                    'users_count_direct' => $userCount,
                    'users_found_via_model' => is_array($users) ? count($users) : 0,
                    'user_stats' => $userStats,
                    'database_platform' => $db->getPlatform(),
                    'database_version' => $db->getVersion(),
                    'table_error' => $tableError,
                    'model_error' => $modelError
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Connection test error: ' . $e->getMessage());

            // Get more detailed error information
            $errorDetails = [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ];

            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Database connection failed: ' . $e->getMessage(),
                'data' => [
                    'database_connected' => false,
                    'error_details' => $errorDetails
                ]
            ]);
        }
    }

    /**
     * Test raw PostgreSQL connection
     */
    public function testRawConnection()
    {
        try {
            $config = config('Database');
            $dbConfig = $config->default;

            // Build connection string
            $connectionString = sprintf(
                "host=%s port=%d dbname=%s user=%s password=%s",
                $dbConfig['hostname'],
                $dbConfig['port'],
                $dbConfig['database'],
                $dbConfig['username'],
                $dbConfig['password']
            );

            // Test raw PostgreSQL connection
            $connection = pg_connect($connectionString);

            if (!$connection) {
                throw new \Exception('Failed to connect to PostgreSQL: ' . pg_last_error());
            }

            // Test simple query
            $result = pg_query($connection, "SELECT 1 as test");
            if (!$result) {
                throw new \Exception('Query failed: ' . pg_last_error($connection));
            }

            $row = pg_fetch_assoc($result);
            pg_close($connection);

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Raw PostgreSQL connection successful',
                'data' => [
                    'connection_string' => str_replace($dbConfig['password'], '***', $connectionString),
                    'query_result' => $row,
                    'php_pgsql_version' => phpversion('pgsql')
                ]
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Raw PostgreSQL connection failed: ' . $e->getMessage(),
                'data' => [
                    'php_pgsql_available' => extension_loaded('pgsql'),
                    'php_pdo_pgsql_available' => extension_loaded('pdo_pgsql')
                ]
            ]);
        }
    }

    /**
     * Display setup instructions
     */
    public function index()
    {
        $data = [
            'title' => 'DCBuyer Setup',
            'supabase_url' => env('supabase.url'),
            'environment' => ENVIRONMENT
        ];

        return view('setup', $data);
    }

    public function testLogin()
    {
        // Create a test session for the admin user
        $session = session();

        $session->set([
            'user_id' => 1,
            'email' => '<EMAIL>',
            'username' => 'admin',
            'fullname' => 'System Administrator',
            'is_admin' => true,
            'is_supervisor' => false,
            'is_buyer' => false,
            'status' => 'active',
            'logged_in' => true
        ]);

        return redirect()->to('admin/users')->with('success', 'Test login successful! You are now logged in as admin.');
    }
}
