<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateProvincesTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'SERIAL',
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'country_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'code' => [
                'type' => 'VARCHAR',
                'constraint' => 10,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('country_id');
        $this->forge->createTable('provinces');

        // Set default timestamps
        $this->db->query('ALTER TABLE provinces ALTER COLUMN created_at SET DEFAULT CURRENT_TIMESTAMP');
        $this->db->query('ALTER TABLE provinces ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP');

        // Add indexes for performance
        $this->db->query('CREATE INDEX idx_provinces_country_id ON provinces(country_id)');
        $this->db->query('CREATE INDEX idx_provinces_name ON provinces(name)');

        // Add foreign key constraint
        if ($this->db->DBDriver === 'Postgre') {
            $this->db->query('ALTER TABLE provinces ADD CONSTRAINT fk_provinces_country FOREIGN KEY (country_id) REFERENCES countries(id) ON DELETE CASCADE');
        }
    }

    public function down()
    {
        // Drop foreign key constraint first
        if ($this->db->DBDriver === 'Postgre') {
            $this->db->query('ALTER TABLE provinces DROP CONSTRAINT IF EXISTS fk_provinces_country');
        }
        
        $this->forge->dropTable('provinces');
    }
}
