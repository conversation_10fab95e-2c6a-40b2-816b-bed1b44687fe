<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="page-header">
    <div class="row align-items-center">
        <div class="col">
            <h3 class="page-title"><?= $page_title ?></h3>
            <p class="text-muted"><?= $page_description ?></p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="<?= base_url('admin/transactions/' . $transaction['id'] . '/edit') ?>" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Edit Transaction
                </a>
                <a href="<?= base_url('admin/transactions') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Details -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Transaction Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">Transaction Code:</td>
                                <td><?= esc($transaction['transaction_code']) ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Transaction Date:</td>
                                <td><?= date('F d, Y', strtotime($transaction['transaction_date'])) ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Status:</td>
                                <td>
                                    <?php
                                    $statusClass = [
                                        'pending' => 'badge-warning',
                                        'completed' => 'badge-success',
                                        'cancelled' => 'badge-danger'
                                    ];
                                    ?>
                                    <span class="badge <?= $statusClass[$transaction['status']] ?? 'badge-secondary' ?>">
                                        <?= ucfirst($transaction['status']) ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Buyer:</td>
                                <td><?= esc($transaction['user_name']) ?></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Mission:</td>
                                <td>
                                    <strong><?= esc($transaction['mission_number']) ?></strong><br>
                                    <?= esc($transaction['mission_name']) ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">Commodity:</td>
                                <td>
                                    <?= esc($transaction['commodity_name']) ?><br>
                                    <small class="text-muted">Unit: <?= esc($transaction['unit_of_measurement']) ?></small>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Customer:</td>
                                <td>
                                    <?php if ($transaction['customer_first_name']): ?>
                                        <?= esc($transaction['customer_first_name'] . ' ' . $transaction['customer_last_name']) ?><br>
                                        <small class="text-muted">Code: <?= esc($transaction['customer_code']) ?></small>
                                    <?php else: ?>
                                        <span class="text-muted">No customer assigned</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Location:</td>
                                <td>
                                    <?= esc($transaction['location_name']) ?><br>
                                    <small class="text-muted">
                                        <?= esc($transaction['district_name']) ?>, <?= esc($transaction['province_name']) ?>, <?= esc($transaction['country_name']) ?>
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Created:</td>
                                <td>
                                    <?= date('F d, Y g:i A', strtotime($transaction['created_at'])) ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transaction Amounts -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Transaction Amounts</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary"><?= number_format($transaction['quantity'], 3) ?></h4>
                            <p class="text-muted mb-0">Quantity</p>
                            <small class="text-muted"><?= esc($transaction['unit_of_measurement']) ?></small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info">PGK <?= number_format($transaction['unit_price'], 2) ?></h4>
                            <p class="text-muted mb-0">Unit Price</p>
                            <small class="text-muted">per <?= esc($transaction['unit_of_measurement']) ?></small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success">PGK <?= number_format($transaction['payment_amount'], 2) ?></h4>
                            <p class="text-muted mb-0">Total Amount</p>
                            <small class="text-muted">Quantity × Unit Price</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning">
                                PGK <?= number_format($transaction['quantity'] * $transaction['unit_price'], 2) ?>
                            </h4>
                            <p class="text-muted mb-0">Calculated</p>
                            <small class="text-muted">Verification</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Remarks -->
        <?php if (!empty($transaction['remarks'])): ?>
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Remarks</h5>
            </div>
            <div class="card-body">
                <p class="mb-0"><?= nl2br(esc($transaction['remarks'])) ?></p>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= base_url('admin/transactions/' . $transaction['id'] . '/edit') ?>" 
                       class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Transaction
                    </a>
                    
                    <?php if ($transaction['status'] === 'pending'): ?>
                    <button type="button" class="btn btn-success" onclick="updateStatus('completed')">
                        <i class="fas fa-check"></i> Mark as Completed
                    </button>
                    <button type="button" class="btn btn-warning" onclick="updateStatus('cancelled')">
                        <i class="fas fa-times"></i> Cancel Transaction
                    </button>
                    <?php endif; ?>
                    
                    <button type="button" class="btn btn-danger" onclick="deleteTransaction()">
                        <i class="fas fa-trash"></i> Delete Transaction
                    </button>
                    
                    <a href="<?= base_url('admin/missions/' . $transaction['mission_id']) ?>" 
                       class="btn btn-outline-info">
                        <i class="fas fa-eye"></i> View Mission
                    </a>
                    
                    <?php if ($transaction['customer_first_name']): ?>
                    <a href="<?= base_url('admin/customers/' . $transaction['customer_id']) ?>" 
                       class="btn btn-outline-secondary">
                        <i class="fas fa-user"></i> View Customer
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Transaction Timeline -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Transaction Timeline</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Transaction Created</h6>
                            <p class="text-muted mb-0">
                                <?= date('M d, Y g:i A', strtotime($transaction['created_at'])) ?>
                            </p>
                        </div>
                    </div>
                    
                    <?php if ($transaction['updated_at'] !== $transaction['created_at']): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Last Updated</h6>
                            <p class="text-muted mb-0">
                                <?= date('M d, Y g:i A', strtotime($transaction['updated_at'])) ?>
                            </p>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($transaction['status'] === 'completed'): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Transaction Completed</h6>
                            <p class="text-muted mb-0">Status: Completed</p>
                        </div>
                    </div>
                    <?php elseif ($transaction['status'] === 'cancelled'): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-danger"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Transaction Cancelled</h6>
                            <p class="text-muted mb-0">Status: Cancelled</p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Transaction Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="statusMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="statusForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="PUT">
                    <input type="hidden" name="status" id="newStatus">
                    <button type="submit" class="btn btn-primary" id="confirmStatusBtn">Update Status</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this transaction? This action cannot be undone and will affect the mission's actual amount.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    padding-left: 10px;
}
</style>

<script>
function updateStatus(status) {
    const statusMessages = {
        'completed': 'Are you sure you want to mark this transaction as completed?',
        'cancelled': 'Are you sure you want to cancel this transaction?'
    };
    
    document.getElementById('statusMessage').textContent = statusMessages[status];
    document.getElementById('newStatus').value = status;
    document.getElementById('statusForm').action = `<?= base_url('admin/transactions/' . $transaction['id']) ?>`;
    
    const statusModal = new bootstrap.Modal(document.getElementById('statusModal'));
    statusModal.show();
}

function deleteTransaction() {
    document.getElementById('deleteForm').action = `<?= base_url('admin/transactions/' . $transaction['id']) ?>`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>

<?= $this->endSection() ?>
