<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateLocationsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'SERIAL',
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'country_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
            ],
            'province_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
            ],
            'district_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
            ],
            'llg_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
            ],
            'ward' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'location_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'gps_latitude' => [
                'type' => 'DECIMAL',
                'constraint' => '10,8',
                'null' => true,
            ],
            'gps_longitude' => [
                'type' => 'DECIMAL',
                'constraint' => '11,8',
                'null' => true,
            ],
            'remarks' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
            ],
            'updated_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
            ],
            'deleted_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'is_deleted' => [
                'type' => 'BOOLEAN',
                'null' => false,
                'default' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey(['country_id', 'province_id', 'district_id', 'llg_id']);
        $this->forge->createTable('locations');

        // Set default timestamps
        $this->db->query('ALTER TABLE locations ALTER COLUMN created_at SET DEFAULT CURRENT_TIMESTAMP');
        $this->db->query('ALTER TABLE locations ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP');

        // Add indexes for performance
        $this->db->query('CREATE INDEX idx_locations_country_id ON locations(country_id)');
        $this->db->query('CREATE INDEX idx_locations_province_id ON locations(province_id)');
        $this->db->query('CREATE INDEX idx_locations_district_id ON locations(district_id)');
        $this->db->query('CREATE INDEX idx_locations_llg_id ON locations(llg_id)');
        $this->db->query('CREATE INDEX idx_locations_ward ON locations(ward)');
        $this->db->query('CREATE INDEX idx_locations_name ON locations(location_name)');
        $this->db->query('CREATE INDEX idx_locations_created_at ON locations(created_at)');
        $this->db->query('CREATE INDEX idx_locations_is_deleted ON locations(is_deleted)');

        // Add foreign key constraints
        if ($this->db->DBDriver === 'Postgre') {
            $this->db->query('ALTER TABLE locations ADD CONSTRAINT fk_locations_country FOREIGN KEY (country_id) REFERENCES countries(id)');
            $this->db->query('ALTER TABLE locations ADD CONSTRAINT fk_locations_province FOREIGN KEY (province_id) REFERENCES provinces(id)');
            $this->db->query('ALTER TABLE locations ADD CONSTRAINT fk_locations_district FOREIGN KEY (district_id) REFERENCES districts(id)');
            $this->db->query('ALTER TABLE locations ADD CONSTRAINT fk_locations_llg FOREIGN KEY (llg_id) REFERENCES llgs(id)');
            $this->db->query('ALTER TABLE locations ADD CONSTRAINT fk_locations_created_by FOREIGN KEY (created_by) REFERENCES users(id)');
        }
    }

    public function down()
    {
        // Drop foreign key constraints first
        if ($this->db->DBDriver === 'Postgre') {
            $this->db->query('ALTER TABLE locations DROP CONSTRAINT IF EXISTS fk_locations_country');
            $this->db->query('ALTER TABLE locations DROP CONSTRAINT IF EXISTS fk_locations_province');
            $this->db->query('ALTER TABLE locations DROP CONSTRAINT IF EXISTS fk_locations_district');
            $this->db->query('ALTER TABLE locations DROP CONSTRAINT IF EXISTS fk_locations_llg');
            $this->db->query('ALTER TABLE locations DROP CONSTRAINT IF EXISTS fk_locations_created_by');
        }
        
        $this->forge->dropTable('locations');
    }
}
