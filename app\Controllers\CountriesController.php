<?php

namespace App\Controllers;

use App\Models\CountryModel;

class CountriesController extends BaseController
{
    protected $countryModel;
    protected $data = [];

    public function __construct()
    {
        $this->countryModel = new CountryModel();
    }

    /**
     * Display list of countries (GET)
     */
    public function index()
    {
        // Get all countries with province count for client-side processing
        $countries = $this->countryModel->select('countries.*, COUNT(provinces.id) as province_count')
                                       ->join('provinces', 'provinces.country_id = countries.id', 'left')
                                       ->groupBy('countries.id')
                                       ->orderBy('countries.name', 'ASC')
                                       ->findAll();

        $this->data['title'] = 'Countries Management - DCBuyer Admin';
        $this->data['active_menu'] = 'countries';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Countries', 'url' => base_url('admin/countries')]
        ];
        
        $this->data['page_title'] = 'Countries Management';
        $this->data['page_description'] = 'Manage countries in the location hierarchy.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/countries/create') . '" class="btn btn-admin-primary">
                <i class="fas fa-plus me-2"></i>Add New Country
            </a>
        ';
        
        $this->data['countries'] = $countries;
        
        return view('admin/countries/countries_index', $this->data);
    }

    /**
     * Show create country form (GET)
     */
    public function create()
    {
        $this->data['title'] = 'Create Country - DCBuyer Admin';
        $this->data['active_menu'] = 'countries';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Countries', 'url' => base_url('admin/countries')],
            ['title' => 'Create Country', 'url' => base_url('admin/countries/create')]
        ];

        $this->data['page_title'] = 'Create New Country';
        $this->data['page_description'] = 'Add a new country to the location hierarchy.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/countries') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Countries
            </a>
        ';

        return view('admin/countries/countries_create', $this->data);
    }

    /**
     * Store new country (POST)
     */
    public function store()
    {
        $rules = [
            'name' => 'required|min_length[2]|max_length[100]',
            'code' => 'required|min_length[2]|max_length[3]|is_unique[countries.code]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'name' => $this->request->getPost('name'),
            'code' => strtoupper($this->request->getPost('code'))
        ];
        
        try {
            $result = $this->countryModel->createCountry($data);
            
            if ($result) {
                return redirect()->to('admin/countries')->with('success', 'Country created successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to create country.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating country: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while creating the country.');
        }
    }

    /**
     * View country details (GET)
     */
    public function show($id)
    {
        $country = $this->countryModel->find($id);

        if (!$country) {
            return redirect()->to('admin/countries')->with('error', 'Country not found.');
        }

        $this->data['country'] = $country;
        $this->data['title'] = 'Country Details - DCBuyer Admin';
        $this->data['active_menu'] = 'countries';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Countries', 'url' => base_url('admin/countries')],
            ['title' => 'Country Details', 'url' => base_url('admin/countries/' . $id)]
        ];
        
        $this->data['page_title'] = 'Country Details: ' . $country['name'];
        $this->data['page_description'] = 'View detailed information about this country.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/countries/' . $id . '/edit') . '" class="btn btn-admin-primary">
                <i class="fas fa-edit me-2"></i>Edit Country
            </a>
            <a href="' . base_url('admin/countries') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Countries
            </a>
        ';
        
        return view('admin/countries/countries_show', $this->data);
    }

    /**
     * Show edit country form (GET)
     */
    public function edit($id)
    {
        $country = $this->countryModel->find($id);

        if (!$country) {
            return redirect()->to('admin/countries')->with('error', 'Country not found.');
        }

        $this->data['country'] = $country;
        $this->data['title'] = 'Edit Country - DCBuyer Admin';
        $this->data['active_menu'] = 'countries';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Countries', 'url' => base_url('admin/countries')],
            ['title' => 'Edit Country', 'url' => base_url('admin/countries/' . $id . '/edit')]
        ];
        
        $this->data['page_title'] = 'Edit Country: ' . $country['name'];
        $this->data['page_description'] = 'Update country information.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/countries/' . $id) . '" class="btn btn-secondary">
                <i class="fas fa-eye me-2"></i>View Details
            </a>
            <a href="' . base_url('admin/countries') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Countries
            </a>
        ';

        return view('admin/countries/countries_edit', $this->data);
    }

    /**
     * Update country (PUT/PATCH)
     */
    public function update($id)
    {
        $country = $this->countryModel->find($id);

        if (!$country) {
            return redirect()->to('admin/countries')->with('error', 'Country not found.');
        }
        
        $rules = [
            'name' => 'required|min_length[2]|max_length[100]',
            'code' => 'required|min_length[2]|max_length[3]|is_unique[countries.code,id,' . $id . ']'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'name' => $this->request->getPost('name'),
            'code' => strtoupper($this->request->getPost('code'))
        ];
        
        try {
            $result = $this->countryModel->update($id, $data);
            
            if ($result) {
                return redirect()->to('admin/countries/' . $id)->with('success', 'Country updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update country.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error updating country: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating the country.');
        }
    }

    /**
     * Delete country (DELETE)
     */
    public function delete($id)
    {
        $country = $this->countryModel->find($id);

        if (!$country) {
            return redirect()->to('admin/countries')->with('error', 'Country not found.');
        }

        try {
            // Check if country has provinces
            $provinceModel = new \App\Models\ProvinceModel();
            $hasProvinces = $provinceModel->where('country_id', $id)->countAllResults() > 0;
            
            if ($hasProvinces) {
                return redirect()->to('admin/countries')->with('error', 'Cannot delete country that has provinces. Please delete all provinces first.');
            }
            
            $result = $this->countryModel->delete($id);
            
            if ($result) {
                return redirect()->to('admin/countries')->with('success', 'Country deleted successfully.');
            } else {
                return redirect()->to('admin/countries')->with('error', 'Failed to delete country.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error deleting country: ' . $e->getMessage());
            return redirect()->to('admin/countries')->with('error', 'An error occurred while deleting the country.');
        }
    }
}
