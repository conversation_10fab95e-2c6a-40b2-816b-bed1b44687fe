<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .form-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .form-section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .form-control:focus {
        border-color: #2E7D32;
        box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
    }
    
    .required {
        color: #dc3545;
    }
    
    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .info-box {
        background-color: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .info-box h6 {
        color: #1976d2;
        margin-bottom: 0.5rem;
    }
    
    .info-box p {
        margin-bottom: 0;
        font-size: 0.9rem;
        color: #424242;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Display Validation Errors -->
    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please correct the following errors:</h6>
            <ul class="mb-0">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Display Error Message -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <form action="<?= base_url('admin/countries') ?>" method="POST" id="countryForm">
        <?= csrf_field() ?>
        
        <!-- Information Box -->
        <div class="info-box">
            <h6><i class="fas fa-info-circle me-2"></i>Country Information</h6>
            <p>Countries are the top level of the location hierarchy. Once created, you can add provinces, districts, LLGs, and specific locations under this country.</p>
        </div>
        
        <!-- Country Details Section -->
        <div class="form-section">
            <div class="form-section-title">
                <i class="fas fa-globe me-2"></i>Country Details
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group">
                        <label for="name" class="form-label">Country Name <span class="required">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="<?= old('name') ?>" required maxlength="100">
                        <div class="form-text">Enter the full name of the country (e.g., Papua New Guinea).</div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="code" class="form-label">Country Code <span class="required">*</span></label>
                        <input type="text" class="form-control" id="code" name="code" 
                               value="<?= old('code') ?>" required maxlength="3" style="text-transform: uppercase;">
                        <div class="form-text">2-3 letter country code (e.g., PNG, AUS).</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>Tips:</h6>
                        <ul class="mb-0">
                            <li>Use standard ISO country codes when possible</li>
                            <li>Country codes must be unique across the system</li>
                            <li>Once created, you can manage provinces under this country</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="form-section">
            <div class="d-flex justify-content-between">
                <a href="<?= base_url('admin/countries') ?>" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
                <button type="submit" class="btn btn-success" id="submitBtn">
                    <i class="fas fa-save me-2"></i>Create Country
                </button>
            </div>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-uppercase country code
    const codeInput = document.getElementById('code');
    codeInput.addEventListener('input', function() {
        this.value = this.value.toUpperCase();
    });

    // Form validation
    document.getElementById('countryForm').addEventListener('submit', function(e) {
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
    });
});
</script>
<?= $this->endSection() ?>
