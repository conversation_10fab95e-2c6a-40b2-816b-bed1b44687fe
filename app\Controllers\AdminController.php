<?php

namespace App\Controllers;

class AdminController extends BaseController
{
    protected $data = [];
    
    public function __construct()
    {
        // Initialize common admin data
        $this->data['user_name'] = session('fullname') ?? session('username') ?? 'Admin';
        $this->data['user_email'] = session('email') ?? '<EMAIL>';
        $this->data['user_role'] = session('is_admin') ? 'administrator' : (session('is_supervisor') ? 'supervisor' : 'user');
        
        // Sample notification data
        $this->data['notification_count'] = 3;
        $this->data['notifications'] = [
            [
                'title' => 'New order received',
                'time' => '2 minutes ago',
                'url' => base_url('admin/orders/247')
            ],
            [
                'title' => 'Low stock alert',
                'time' => '15 minutes ago',
                'url' => base_url('admin/inventory')
            ],
            [
                'title' => 'New user registration',
                'time' => '1 hour ago',
                'url' => base_url('admin/users')
            ]
        ];
        
        // Sample pending orders count
        $this->data['pending_orders'] = 5;
    }
    
    public function dashboard()
    {
        $this->data['title'] = 'Admin Dashboard - DCBuyer';
        $this->data['description'] = 'DCBuyer Administration Panel Dashboard';
        $this->data['active_menu'] = 'dashboard';
        
        // Breadcrumbs
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')]
        ];
        
        // Page header
        $this->data['page_title'] = 'Dashboard';
        $this->data['page_description'] = 'Welcome to the DCBuyer administration panel. Monitor your platform performance and manage operations.';
        $this->data['page_actions'] = '
            <button class="btn btn-admin-primary" onclick="window.location.reload()">
                <i class="fas fa-sync-alt me-2"></i>Refresh
            </button>
        ';
        
        return view('admin/admin_dashboard', $this->data);
    }
    
    public function orders()
    {
        $this->data['title'] = 'Orders Management - DCBuyer Admin';
        $this->data['active_menu'] = 'orders';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Orders', 'url' => base_url('admin/orders')]
        ];
        
        $this->data['page_title'] = 'Orders Management';
        $this->data['page_description'] = 'Monitor and manage all commodity orders on your platform.';
        
        return view('admin/orders_list', $this->data);
    }
    
    public function buy()
    {
        // Load required models
        $missionModel = new \App\Models\MissionModel();
        $transactionModel = new \App\Models\TransactionModel();

        // Get current user ID from session
        $currentUserId = session()->get('user_id');

        if (!$currentUserId) {
            return redirect()->to('login')->with('error', 'Please log in to access this page.');
        }

        // Get missions assigned to current user with details
        $missions = $missionModel->select('mission.*,
                                         commodities.commodity_name,
                                         commodities.unit_of_measurement,
                                         locations.location_name,
                                         districts.name as district_name,
                                         provinces.name as province_name,
                                         countries.name as country_name')
                                ->join('commodities', 'commodities.commodity_id = mission.commodity_id', 'left')
                                ->join('locations', 'locations.id = mission.location_id', 'left')
                                ->join('districts', 'districts.id = locations.district_id', 'left')
                                ->join('provinces', 'provinces.id = locations.province_id', 'left')
                                ->join('countries', 'countries.id = locations.country_id', 'left')
                                ->where('mission.user_id', $currentUserId)
                                ->orderBy('mission.mission_date', 'DESC')
                                ->findAll();

        // Calculate mission statistics for current user
        $totalMissions = count($missions);
        $inProgressMissions = count(array_filter($missions, fn($m) => $m['mission_status'] === 'in_progress'));
        $completedMissions = count(array_filter($missions, fn($m) => $m['mission_status'] === 'completed'));
        $pendingMissions = count(array_filter($missions, fn($m) => $m['mission_status'] === 'pending'));

        $stats = [
            'total_missions' => $totalMissions,
            'in_progress_missions' => $inProgressMissions,
            'completed_missions' => $completedMissions,
            'pending_missions' => $pendingMissions
        ];

        // Get transaction statistics for current user
        $transactionStats = [
            'total_transactions' => $transactionModel->where('user_id', $currentUserId)->countAllResults(),
            'pending_transactions' => $transactionModel->where('user_id', $currentUserId)->where('status', 'pending')->countAllResults(),
            'completed_transactions' => $transactionModel->where('user_id', $currentUserId)->where('status', 'completed')->countAllResults(),
            'total_amount' => $transactionModel->selectSum('payment_amount')->where('user_id', $currentUserId)->where('status', 'completed')->first()['payment_amount'] ?? 0
        ];

        $this->data['title'] = 'My Missions - DCBuyer';
        $this->data['active_menu'] = 'buy';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'My Missions', 'url' => base_url('admin/buy')]
        ];

        $this->data['page_title'] = 'My Missions';
        $this->data['page_description'] = 'View and manage missions assigned to you. Create transactions for your assigned commodities.';

        $this->data['missions'] = $missions;
        $this->data['stats'] = $stats;
        $this->data['transaction_stats'] = $transactionStats;
        $this->data['current_user_id'] = $currentUserId;

        return view('admin/buy_missions', $this->data);
    }
    
    public function analytics()
    {
        $this->data['title'] = 'Analytics - DCBuyer Admin';
        $this->data['active_menu'] = 'analytics';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Analytics', 'url' => base_url('admin/analytics')]
        ];
        
        $this->data['page_title'] = 'Analytics & Reports';
        $this->data['page_description'] = 'View detailed analytics and generate reports for your platform.';
        
        return view('admin/analytics', $this->data);
    }

    public function farmers()
    {
        // Load UserModel
        $userModel = new \App\Models\UserModel();

        // Get farmers (users with is_buyer = true but not admin/supervisor)
        $this->data['farmers'] = $userModel->where('is_buyer', true)
                                          ->where('is_admin', false)
                                          ->where('is_supervisor', false)
                                          ->findAll();

        $this->data['title'] = 'Farmers Management - DCBuyer Admin';
        $this->data['active_menu'] = 'farmers';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Farmers', 'url' => base_url('admin/farmers')]
        ];

        $this->data['page_title'] = 'Farmers Management';
        $this->data['page_description'] = 'Manage farmer accounts and their agricultural products.';

        return view('admin/farmers_list', $this->data);
    }

    public function buyers()
    {
        // Load UserModel
        $userModel = new \App\Models\UserModel();

        // Get buyers (users with is_buyer = true)
        $this->data['buyers'] = $userModel->where('is_buyer', true)->findAll();

        $this->data['title'] = 'Buyers Management - DCBuyer Admin';
        $this->data['active_menu'] = 'buyers';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Buyers', 'url' => base_url('admin/buyers')]
        ];

        $this->data['page_title'] = 'Buyers Management';
        $this->data['page_description'] = 'Manage buyer accounts and their purchasing activities.';

        return view('admin/buyers_list', $this->data);
    }

    public function transactions()
    {
        $this->data['title'] = 'Transactions - DCBuyer Admin';
        $this->data['active_menu'] = 'transactions';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Transactions', 'url' => base_url('admin/transactions')]
        ];

        $this->data['page_title'] = 'Transaction Management';
        $this->data['page_description'] = 'Monitor and manage all financial transactions.';

        return view('admin/transactions_list', $this->data);
    }

    public function inventory()
    {
        $this->data['title'] = 'Inventory Management - DCBuyer Admin';
        $this->data['active_menu'] = 'inventory';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Inventory', 'url' => base_url('admin/inventory')]
        ];

        $this->data['page_title'] = 'Inventory Management';
        $this->data['page_description'] = 'Track and manage commodity inventory levels.';

        return view('admin/inventory_list', $this->data);
    }

    public function categories()
    {
        $this->data['title'] = 'Categories Management - DCBuyer Admin';
        $this->data['active_menu'] = 'categories';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Categories', 'url' => base_url('admin/categories')]
        ];

        $this->data['page_title'] = 'Commodity Categories';
        $this->data['page_description'] = 'Manage commodity categories and classifications.';

        return view('admin/categories_list', $this->data);
    }

    public function settings()
    {
        $this->data['title'] = 'Settings - DCBuyer Admin';
        $this->data['active_menu'] = 'settings';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Settings', 'url' => base_url('admin/settings')]
        ];

        $this->data['page_title'] = 'System Settings';
        $this->data['page_description'] = 'Configure system settings and preferences.';

        return view('admin/settings', $this->data);
    }

    public function notifications()
    {
        $this->data['title'] = 'Notifications - DCBuyer Admin';
        $this->data['active_menu'] = 'notifications';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Notifications', 'url' => base_url('admin/notifications')]
        ];

        $this->data['page_title'] = 'Notification Center';
        $this->data['page_description'] = 'Manage system notifications and alerts.';

        return view('admin/notifications_list', $this->data);
    }

    public function system()
    {
        $this->data['title'] = 'System Information - DCBuyer Admin';
        $this->data['active_menu'] = 'system';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'System', 'url' => base_url('admin/system')]
        ];

        $this->data['page_title'] = 'System Information';
        $this->data['page_description'] = 'View system status and configuration details.';

        return view('admin/system_info', $this->data);
    }
}