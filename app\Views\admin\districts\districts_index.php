<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<!-- SheetJS for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<style>
    /* Custom Table Controls Styling */
    .table-controls {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-controls input[type="text"], .table-controls select {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 0.5rem;
    }

    .table-controls input[type="text"]:focus, .table-controls select:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .table-pagination {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-info {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .pagination .page-link {
        border-radius: 8px;
        margin: 0 2px;
        border: 1px solid #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
    }

    .table-responsive {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .table th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
        padding: 1rem 0.75rem;
    }

    .table td {
        padding: 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f3f4;
    }

    .btn-action {
        margin: 0 2px;
        padding: 0.25rem 0.5rem;
    }

    .badge-country {
        background-color: #e3f2fd;
        color: #1976d2;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .badge-province {
        background-color: #f3e5f5;
        color: #7b1fa2;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .district-stats {
        font-size: 0.875rem;
        color: #6c757d;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Search and Filter Controls -->
    <div class="table-controls mb-4">
        <form method="GET" action="<?= base_url('admin/districts') ?>">
            <div class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search Districts</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?= esc($search) ?>" placeholder="Search by name, code, province, or country...">
                </div>
                <div class="col-md-3">
                    <label for="province_id" class="form-label">Filter by Province</label>
                    <select class="form-select" id="province_id" name="province_id">
                        <option value="">All Provinces</option>
                        <?php foreach ($provinces as $province): ?>
                            <option value="<?= $province['id'] ?>" <?= $selected_province == $province['id'] ? 'selected' : '' ?>>
                                <?= esc($province['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                    <a href="<?= base_url('admin/districts') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>Clear
                    </a>
                </div>
                <div class="col-md-2 text-end">
                    <button type="button" class="btn btn-success" onclick="exportToExcel()">
                        <i class="fas fa-file-excel me-2"></i>Export to Excel
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Districts Table -->
    <div class="table-responsive">
        <table class="table table-hover mb-0" id="districtsTable">
            <thead>
                <tr>
                    <th>District Name</th>
                    <th>Province</th>
                    <th>Country</th>
                    <th>District Code</th>
                    <th>LLGs</th>
                    <th>Created Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($districts)): ?>
                    <?php foreach ($districts as $district): ?>
                        <tr>
                            <td>
                                <strong><?= esc($district['name']) ?></strong>
                            </td>
                            <td>
                                <span class="badge-province"><?= esc($district['province_name']) ?></span>
                            </td>
                            <td>
                                <span class="badge-country"><?= esc($district['country_name']) ?></span>
                            </td>
                            <td>
                                <?= $district['code'] ? esc($district['code']) : '<span class="text-muted">—</span>' ?>
                            </td>
                            <td>
                                <div class="district-stats">
                                    <?php 
                                    // This would need to be calculated in the controller
                                    echo '— LLGs';
                                    ?>
                                </div>
                            </td>
                            <td><?= date('M j, Y', strtotime($district['created_at'])) ?></td>
                            <td>
                                <a href="<?= base_url('admin/districts/' . $district['id']) ?>" 
                                   class="btn btn-sm btn-outline-primary btn-action" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?= base_url('admin/districts/' . $district['id'] . '/edit') ?>" 
                                   class="btn btn-sm btn-outline-warning btn-action" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger btn-action" 
                                        onclick="confirmDelete(<?= $district['id'] ?>, '<?= esc($district['name']) ?>')" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-map-marked-alt fa-3x mb-3"></i>
                                <h5>No districts found</h5>
                                <p>No districts match your search criteria.</p>
                                <a href="<?= base_url('admin/districts/create') ?>" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Add First District
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php if ($pager): ?>
        <div class="table-pagination mt-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="table-info">
                    Showing <?= count($districts) ?> districts
                </div>
                <div>
                    <?= $pager->links() ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the district "<span id="districtName"></span>"?</p>
                <p class="text-danger"><small>This action cannot be undone and will also delete all related LLGs and locations.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete District</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize table functionality if needed
});

function confirmDelete(districtId, districtName) {
    document.getElementById('districtName').textContent = districtName;
    document.getElementById('deleteForm').action = '<?= base_url('admin/districts') ?>/' + districtId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function exportToExcel() {
    const table = document.getElementById('districtsTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Districts"});
    XLSX.writeFile(wb, 'districts_export_' + new Date().toISOString().slice(0,10) + '.xlsx');
}
</script>
<?= $this->endSection() ?>
