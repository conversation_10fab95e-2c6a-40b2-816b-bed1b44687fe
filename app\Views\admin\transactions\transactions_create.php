<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- Transaction Form -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Transaction Information</h5>
            </div>
            <div class="card-body">
                <?= form_open('admin/transactions', ['id' => 'transactionForm']) ?>
                    <?= csrf_field() ?>

                    <!-- Hidden fields for auto-populated values -->
                    <input type="hidden" name="user_id" value="<?= $transaction['user_id'] ?>">
                    <input type="hidden" name="mission_id" value="<?= $transaction['mission_id'] ?>">
                    <input type="hidden" name="commodity_id" value="<?= $transaction['commodity_id'] ?>">
                    <input type="hidden" name="location_id" value="<?= $transaction['location_id'] ?>">

                    <!-- Display auto-populated information -->
                    <div class="alert alert-info">
                        <h6 class="mb-2"><i class="fas fa-info-circle"></i> Transaction Information</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Buyer:</strong> <?= esc($current_user['fullname']) ?><br>
                                <strong>Mission:</strong> <?= esc($current_mission['mission_number']) ?> - <?= esc($current_mission['mission_name']) ?>
                            </div>
                            <div class="col-md-6">
                                <strong>Commodity:</strong> <?= esc($current_mission['commodity_name']) ?><br>
                                <strong>Location:</strong> <?= esc($current_mission['location_hierarchy']) ?>
                            </div>
                        </div>
                    </div>

                    <!-- Customer selection (optional) -->
                    <div class="form-group mb-3">
                        <label for="customer_id" class="form-label">Customer (Optional)</label>
                        <select class="form-control <?= isset($errors['customer_id']) ? 'is-invalid' : '' ?>"
                                id="customer_id" name="customer_id">
                            <option value="">No Customer</option>
                            <?php foreach ($customers as $customer): ?>
                                <option value="<?= $customer['id'] ?>"
                                        <?= old('customer_id', $transaction['customer_id']) == $customer['id'] ? 'selected' : '' ?>>
                                    <?= esc($customer['first_name'] . ' ' . $customer['last_name']) ?> (<?= esc($customer['customer_code']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($errors['customer_id'])): ?>
                            <div class="invalid-feedback"><?= $errors['customer_id'] ?></div>
                        <?php endif; ?>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="quantity" class="form-label">Quantity <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" step="0.001" min="0.001"
                                           class="form-control <?= isset($errors['quantity']) ? 'is-invalid' : '' ?>"
                                           id="quantity" name="quantity"
                                           value="<?= old('quantity', $transaction['quantity']) ?>"
                                           placeholder="0.000" required>
                                    <span class="input-group-text"><?= esc($current_mission['unit_of_measurement']) ?></span>
                                </div>
                                <?php if (isset($errors['quantity'])): ?>
                                    <div class="invalid-feedback"><?= $errors['quantity'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="unit_price" class="form-label">Unit Price (per <?= esc($current_mission['unit_of_measurement']) ?>) <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">PGK</span>
                                    <input type="number" step="0.01" min="0.01"
                                           class="form-control <?= isset($errors['unit_price']) ? 'is-invalid' : '' ?>"
                                           id="unit_price" name="unit_price"
                                           value="<?= old('unit_price', $transaction['unit_price']) ?>"
                                           placeholder="0.00" required>
                                </div>
                                <?php if (isset($errors['unit_price'])): ?>
                                    <div class="invalid-feedback"><?= $errors['unit_price'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="payment_amount" class="form-label">Total Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">PGK</span>
                                    <input type="number" step="0.01" min="0.01"
                                           class="form-control"
                                           id="payment_amount" name="payment_amount"
                                           value="<?= old('payment_amount', $transaction['payment_amount']) ?>"
                                           placeholder="0.00" readonly>
                                </div>
                                <small class="form-text text-muted">Automatically calculated: Quantity × Unit Price</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="transaction_date" class="form-label">Transaction Date <span class="text-danger">*</span></label>
                                <input type="date" 
                                       class="form-control <?= isset($errors['transaction_date']) ? 'is-invalid' : '' ?>" 
                                       id="transaction_date" name="transaction_date" 
                                       value="<?= old('transaction_date', $transaction['transaction_date']) ?>" 
                                       required>
                                <?php if (isset($errors['transaction_date'])): ?>
                                    <div class="invalid-feedback"><?= $errors['transaction_date'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="status" class="form-label">Status</label>
                                <input type="text" class="form-control" value="Completed" readonly>
                                <input type="hidden" name="status" value="completed">
                                <small class="form-text text-muted">Transactions are automatically marked as completed</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control <?= isset($errors['remarks']) ? 'is-invalid' : '' ?>" 
                                  id="remarks" name="remarks" rows="3" 
                                  placeholder="Additional notes or comments..."><?= old('remarks', $transaction['remarks']) ?></textarea>
                        <?php if (isset($errors['remarks'])): ?>
                            <div class="invalid-feedback"><?= $errors['remarks'] ?></div>
                        <?php endif; ?>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Create Transaction
                        </button>
                        <a href="<?= base_url('admin/transactions') ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>

                <?= form_close() ?>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const quantityInput = document.getElementById('quantity');
    const unitPriceInput = document.getElementById('unit_price');
    const paymentAmountInput = document.getElementById('payment_amount');

    // Calculate total amount
    function calculateTotal() {
        const quantity = parseFloat(quantityInput.value) || 0;
        const unitPrice = parseFloat(unitPriceInput.value) || 0;
        const total = quantity * unitPrice;
        paymentAmountInput.value = total.toFixed(2);
    }

    // Add event listeners for automatic calculation
    quantityInput.addEventListener('input', calculateTotal);
    unitPriceInput.addEventListener('input', calculateTotal);
});
</script>

<?= $this->endSection() ?>
