<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .customer-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    
    .customer-header {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .customer-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2E7D32;
        margin-bottom: 0.5rem;
    }
    
    .customer-code {
        font-size: 1.2rem;
        font-weight: 600;
        color: #666;
    }
    
    .info-section {
        margin-bottom: 2rem;
    }
    
    .info-section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e9ecef;
    }
    
    .info-item {
        margin-bottom: 1rem;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
    }
    
    .info-value {
        color: #6c757d;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-active {
        background: linear-gradient(135deg, #4CAF50, #8BC34A);
        color: white;
    }
    
    .status-inactive {
        background: linear-gradient(135deg, #FF9800, #FFC107);
        color: white;
    }
    
    .contact-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
    }
    
    .contact-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    
    .contact-item:last-child {
        margin-bottom: 0;
    }
    
    .contact-icon {
        width: 20px;
        margin-right: 0.75rem;
        color: #2E7D32;
    }
    
    .transaction-table {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .no-data {
        text-align: center;
        padding: 2rem;
        color: #6c757d;
    }
    
    .no-data i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .quick-actions {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
    }
    
    .quick-actions .btn {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <!-- Customer Information -->
        <div class="col-lg-8">
            <div class="customer-card">
                <div class="customer-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="customer-title">
                                <?= esc($customer['first_name'] . ' ' . $customer['last_name']) ?>
                            </div>
                            <div class="customer-code">
                                Customer Code: <?= esc($customer['customer_code']) ?>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="status-badge status-<?= $customer['status'] ?>">
                                <?= ucfirst($customer['status']) ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Personal Information -->
                <div class="info-section">
                    <h5 class="info-section-title">
                        <i class="fas fa-user me-2"></i>Personal Information
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">First Name</div>
                                <div class="info-value"><?= esc($customer['first_name']) ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">Last Name</div>
                                <div class="info-value"><?= esc($customer['last_name']) ?></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="info-section">
                    <h5 class="info-section-title">
                        <i class="fas fa-address-book me-2"></i>Contact Information
                    </h5>
                    
                    <div class="contact-info">
                        <?php if ($customer['phone']): ?>
                            <div class="contact-item">
                                <i class="fas fa-phone contact-icon"></i>
                                <div>
                                    <strong>Phone:</strong> 
                                    <a href="tel:<?= esc($customer['phone']) ?>"><?= esc($customer['phone']) ?></a>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($customer['email']): ?>
                            <div class="contact-item">
                                <i class="fas fa-envelope contact-icon"></i>
                                <div>
                                    <strong>Email:</strong> 
                                    <a href="mailto:<?= esc($customer['email']) ?>"><?= esc($customer['email']) ?></a>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($customer['address']): ?>
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt contact-icon"></i>
                                <div>
                                    <strong>Address:</strong><br>
                                    <?= nl2br(esc($customer['address'])) ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!$customer['phone'] && !$customer['email'] && !$customer['address']): ?>
                            <div class="text-muted text-center">
                                <i class="fas fa-info-circle me-2"></i>No contact information available
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Location Information -->
                <?php if ($customer['location_name']): ?>
                <div class="info-section">
                    <h5 class="info-section-title">
                        <i class="fas fa-map-marker-alt me-2"></i>Location Information
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">Location</div>
                                <div class="info-value"><?= esc($customer['location_name']) ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">Ward</div>
                                <div class="info-value"><?= esc($customer['ward'] ?? '-') ?></div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="info-item">
                                <div class="info-label">Full Hierarchy</div>
                                <div class="info-value">
                                    <?= esc($customer['country_name']) ?> > 
                                    <?= esc($customer['province_name']) ?> > 
                                    <?= esc($customer['district_name']) ?> > 
                                    <?= esc($customer['llg_name']) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Audit Information -->
                <div class="info-section">
                    <h5 class="info-section-title">
                        <i class="fas fa-history me-2"></i>Audit Information
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">Created</div>
                                <div class="info-value">
                                    <?= date('F j, Y \a\t g:i A', strtotime($customer['created_at'])) ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">Last Updated</div>
                                <div class="info-value">
                                    <?= date('F j, Y \a\t g:i A', strtotime($customer['updated_at'])) ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar with Quick Actions -->
        <div class="col-lg-4">
            <div class="customer-card">
                <h5 class="info-section-title">
                    <i class="fas fa-tools me-2"></i>Quick Actions
                </h5>
                
                <div class="quick-actions">
                    <a href="<?= base_url('admin/customers/' . $customer['id'] . '/edit') ?>" 
                       class="btn btn-admin-primary">
                        <i class="fas fa-edit me-2"></i>Edit Customer
                    </a>
                    
                    <?php if ($customer['status'] === 'active'): ?>
                        <button type="button" class="btn btn-warning" 
                                onclick="confirmStatusChange(<?= $customer['id'] ?>, 'deactivate', '<?= esc($customer['first_name'] . ' ' . $customer['last_name']) ?>')">
                            <i class="fas fa-pause me-2"></i>Deactivate
                        </button>
                    <?php else: ?>
                        <button type="button" class="btn btn-info" 
                                onclick="confirmStatusChange(<?= $customer['id'] ?>, 'activate', '<?= esc($customer['first_name'] . ' ' . $customer['last_name']) ?>')">
                            <i class="fas fa-play me-2"></i>Activate
                        </button>
                    <?php endif; ?>
                    
                    <a href="<?= base_url('admin/transactions?customer_id=' . $customer['id']) ?>" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-credit-card me-2"></i>View Transactions
                    </a>
                    
                    <button type="button" class="btn btn-outline-danger" 
                            onclick="confirmDelete(<?= $customer['id'] ?>, '<?= esc($customer['first_name'] . ' ' . $customer['last_name']) ?>')">
                        <i class="fas fa-trash me-2"></i>Delete Customer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="row">
        <div class="col-12">
            <div class="transaction-table">
                <div class="table-header p-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">Recent Transactions</h5>
                            <small class="text-muted">Last 10 transactions involving this customer</small>
                        </div>
                        <a href="<?= base_url('admin/transactions?customer_id=' . $customer['id']) ?>" 
                           class="btn btn-sm btn-outline-primary">
                            View All Transactions
                        </a>
                    </div>
                </div>

                <?php if (empty($transactions)): ?>
                <div class="no-data">
                    <i class="fas fa-receipt"></i>
                    <h6>No Transactions Found</h6>
                    <p class="mb-0">This customer has no transaction history yet.</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>Transaction Code</th>
                                <th>Date</th>
                                <th>Commodity</th>
                                <th>Quantity</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($transactions as $transaction): ?>
                            <tr>
                                <td>
                                    <span class="fw-bold text-primary"><?= esc($transaction['transaction_code']) ?></span>
                                </td>
                                <td>
                                    <?= date('M j, Y', strtotime($transaction['transaction_date'])) ?>
                                </td>
                                <td>
                                    <?= esc($transaction['commodity_name'] ?? '-') ?>
                                </td>
                                <td>
                                    <?= number_format($transaction['quantity'], 2) ?> 
                                    <?= esc($transaction['unit_of_measurement'] ?? '') ?>
                                </td>
                                <td>
                                    <strong>$<?= number_format($transaction['payment_amount'], 2) ?></strong>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $transaction['status'] === 'completed' ? 'success' : ($transaction['status'] === 'pending' ? 'warning' : 'secondary') ?>">
                                        <?= ucfirst(esc($transaction['status'])) ?>
                                    </span>
                                </td>
                                <td>
                                    <a href="<?= base_url('admin/transactions/' . $transaction['id']) ?>" 
                                       class="btn btn-sm btn-outline-primary" title="View Transaction">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Status Change Confirmation Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalTitle">Confirm Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="statusModalMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="statusForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="PATCH">
                    <button type="submit" class="btn" id="statusConfirmBtn">Confirm</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the customer <strong id="customerName"></strong>?</p>
                <p class="text-muted">This action cannot be undone and may affect transaction records.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete Customer</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function confirmStatusChange(customerId, action, customerName) {
    const modal = document.getElementById('statusModal');
    const title = document.getElementById('statusModalTitle');
    const message = document.getElementById('statusModalMessage');
    const confirmBtn = document.getElementById('statusConfirmBtn');
    const form = document.getElementById('statusForm');

    if (action === 'activate') {
        title.textContent = 'Confirm Activation';
        message.textContent = `Are you sure you want to activate customer "${customerName}"?`;
        confirmBtn.textContent = 'Activate';
        confirmBtn.className = 'btn btn-info';
        form.action = '<?= base_url('admin/customers') ?>/' + customerId + '/activate';
    } else {
        title.textContent = 'Confirm Deactivation';
        message.textContent = `Are you sure you want to deactivate customer "${customerName}"?`;
        confirmBtn.textContent = 'Deactivate';
        confirmBtn.className = 'btn btn-warning';
        form.action = '<?= base_url('admin/customers') ?>/' + customerId + '/deactivate';
    }

    var statusModal = new bootstrap.Modal(modal);
    statusModal.show();
}

function confirmDelete(customerId, customerName) {
    document.getElementById('customerName').textContent = customerName;
    document.getElementById('deleteForm').action = '<?= base_url('admin/customers') ?>/' + customerId + '/delete';

    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
<?= $this->endSection() ?>