<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<!-- SheetJS for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<style>
    /* Custom Table Controls Styling */
    .table-controls {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-controls input[type="text"] {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 0.5rem;
    }

    .table-controls input[type="text"]:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .table-pagination {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-info {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .pagination .page-link {
        border-radius: 8px;
        margin: 0 2px;
        border: 1px solid #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
    }

<style>
    .stats-card {
        background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
        border-radius: 12px;
        padding: 1.5rem;
        color: white;
        margin-bottom: 1.5rem;
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .commodity-table {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .table-header {
        background: #f8f9fa;
        padding: 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .search-box {
        max-width: 300px;
    }
    
    .unit-badge {
        background: #e3f2fd;
        color: #1976d2;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .action-buttons .btn {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .table th {
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
    }
    
    .table td {
        vertical-align: middle;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number"><?= $stats['total_commodities'] ?></div>
                <div class="stats-label">Total Commodities</div>
            </div>
        </div>
        <div class="col-md-9">
            <div class="stats-card">
                <h6 class="mb-3">Unit Distribution</h6>
                <div class="row">
                    <?php foreach (array_slice($stats['unit_distribution'], 0, 4) as $unit): ?>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h5 mb-1"><?= $unit['count'] ?></div>
                            <small><?= strtoupper($unit['unit_of_measurement']) ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Commodities Table -->
    <div class="commodity-table">
        <div class="table-header">
            <div class="row align-items-center">
                <div class="col-md-12">
                    <h5 class="mb-0">Commodities List</h5>
                    <small class="text-muted">Manage all commodities in your platform</small>
                </div>
            </div>
        </div>

        <?php if (empty($commodities)): ?>
        <div class="empty-state">
            <i class="fas fa-seedling"></i>
            <h5>No Commodities Found</h5>
            <p class="mb-3">
                <?php if ($search): ?>
                    No commodities match your search criteria.
                <?php else: ?>
                    Start by adding your first commodity to the platform.
                <?php endif; ?>
            </p>
            <a href="<?= base_url('admin/commodities/create') ?>" class="btn btn-admin-primary">
                <i class="fas fa-plus me-2"></i>Add First Commodity
            </a>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table id="commoditiesTable" class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Commodity Name</th>
                        <th>Unit of Measurement</th>
                        <th>Remarks</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($commodities as $commodity): ?>
                    <tr>
                        <td>
                            <span class="fw-bold text-primary">#<?= $commodity['commodity_id'] ?></span>
                        </td>
                        <td>
                            <div class="fw-bold"><?= esc($commodity['commodity_name']) ?></div>
                        </td>
                        <td>
                            <span class="unit-badge"><?= strtoupper(esc($commodity['unit_of_measurement'])) ?></span>
                        </td>
                        <td>
                            <span class="text-muted">
                                <?= $commodity['remarks'] ? esc(substr($commodity['remarks'], 0, 50)) . (strlen($commodity['remarks']) > 50 ? '...' : '') : '-' ?>
                            </span>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?= date('M j, Y', strtotime($commodity['created_at'])) ?>
                            </small>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="<?= base_url('admin/commodities/' . $commodity['commodity_id']) ?>" 
                                   class="btn btn-sm btn-outline-primary" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?= base_url('admin/commodities/' . $commodity['commodity_id'] . '/edit') ?>" 
                                   class="btn btn-sm btn-outline-success" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="confirmDelete(<?= $commodity['commodity_id'] ?>, '<?= esc($commodity['commodity_name']) ?>')"
                                        title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the commodity <strong id="commodityName"></strong>?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete Commodity</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Initialize enhanced table functionality using existing admin template pattern
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedTable('commoditiesTable', {
        searchPlaceholder: 'Search commodities by name, unit, or remarks...',
        exportFilename: 'commodities_report',
        rowsPerPage: 25
    });
});

// Enhanced table functionality implementation
function initializeEnhancedTable(tableId, options = {}) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const config = {
        searchPlaceholder: options.searchPlaceholder || 'Search...',
        exportFilename: options.exportFilename || 'table_export',
        rowsPerPage: options.rowsPerPage || 25,
        ...options
    };

    // Add search functionality
    addTableSearch(tableId, config);

    // Add export functionality
    addExportButton(tableId, config);

    // Add pagination
    addTablePagination(tableId, config);

    // Initialize existing admin template table features
    AdminTemplate.initializeDataTable(tableId, {
        sortable: true,
        searchable: true
    });
}

function addTableSearch(tableId, config) {
    const table = document.getElementById(tableId);
    const tableContainer = table.closest('.table-responsive').parentNode;

    // Create search container
    const searchContainer = document.createElement('div');
    searchContainer.className = 'table-controls mb-3 d-flex justify-content-between align-items-center';

    // Create search input
    const searchDiv = document.createElement('div');
    searchDiv.className = 'd-flex align-items-center';
    searchDiv.innerHTML = `
        <label class="me-2 mb-0">Search:</label>
        <input type="text" id="${tableId}Search" class="form-control" style="width: 300px;" placeholder="${config.searchPlaceholder}">
    `;

    // Create export button
    const exportDiv = document.createElement('div');
    exportDiv.innerHTML = `
        <button type="button" id="${tableId}ExportExcel" class="btn btn-success">
            <i class="fas fa-file-excel me-2"></i>Export to Excel
        </button>
    `;

    searchContainer.appendChild(searchDiv);
    searchContainer.appendChild(exportDiv);
    tableContainer.insertBefore(searchContainer, tableContainer.firstChild);

    // Add search functionality
    const searchInput = document.getElementById(`${tableId}Search`);
    searchInput.addEventListener('input', function() {
        filterTable(tableId, this.value.toLowerCase());
    });
}

function filterTable(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    updateTableInfo(tableId);
}

function addExportButton(tableId, config) {
    const exportBtn = document.getElementById(`${tableId}ExportExcel`);
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportTableToExcel(tableId, config.exportFilename);
        });
    }
}

function exportTableToExcel(tableId, filename) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tr');
    const data = [];

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll(index === 0 ? 'th' : 'td');
        const rowData = [];

        cells.forEach((cell, cellIndex) => {
            // Skip the Actions column (last)
            if (cellIndex < cells.length - 1) {
                let cellText = cell.textContent.trim();
                // Clean up unit badges
                if (cell.querySelector('.unit-badge')) {
                    cellText = cell.querySelector('.unit-badge').textContent.trim();
                }
                rowData.push(cellText);
            }
        });

        if (rowData.length > 0) {
            data.push(rowData);
        }
    });

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(data);

    // Set column widths for commodities table
    const colWidths = [
        { wch: 8 },  // ID
        { wch: 30 }, // Commodity Name
        { wch: 20 }, // Unit of Measurement
        { wch: 40 }, // Remarks
        { wch: 15 }  // Created
    ];
    ws['!cols'] = colWidths;

    // Style the header row
    const headerRange = XLSX.utils.decode_range(ws['!ref']);
    for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!ws[cellAddress]) continue;
        ws[cellAddress].s = {
            font: { bold: true, color: { rgb: "FFFFFF" } },
            fill: { fgColor: { rgb: "2E7D32" } },
            alignment: { horizontal: "center" }
        };
    }

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, "Commodities Report");

    // Generate filename with current date
    const exportFilename = filename + '_' + new Date().toISOString().split('T')[0] + '.xlsx';

    // Save the file
    XLSX.writeFile(wb, exportFilename);

    AdminTemplate.showNotification('Excel file exported successfully!', 'success');
}

function addTablePagination(tableId, config) {
    const table = document.getElementById(tableId);
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    if (rows.length <= config.rowsPerPage) return; // No pagination needed for small tables

    let currentPage = 1;
    const rowsPerPage = config.rowsPerPage;
    const totalPages = Math.ceil(rows.length / rowsPerPage);

    // Create pagination container
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'table-pagination mt-3 d-flex justify-content-between align-items-center';
    paginationContainer.innerHTML = `
        <div class="table-info">
            <span id="${tableId}TableInfo">Showing 1 to ${Math.min(rowsPerPage, rows.length)} of ${rows.length} entries</span>
        </div>
        <nav>
            <ul class="pagination mb-0" id="${tableId}TablePagination">
                <!-- Pagination buttons will be generated here -->
            </ul>
        </nav>
    `;

    table.parentNode.appendChild(paginationContainer);

    function showPage(page) {
        const start = (page - 1) * rowsPerPage;
        const end = start + rowsPerPage;

        rows.forEach((row, index) => {
            if (index >= start && index < end && row.style.display !== 'none') {
                row.style.display = '';
            } else if (row.style.display !== 'none') {
                row.style.display = 'none';
            }
        });

        updatePagination(tableId, page, totalPages, showPage);
        updateTableInfo(tableId);
    }

    // Initialize first page
    showPage(1);
}

function updatePagination(tableId, currentPage, totalPages, showPageCallback) {
    const pagination = document.getElementById(`${tableId}TablePagination`);
    pagination.innerHTML = '';

    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>`;
    pagination.appendChild(prevLi);

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
        pagination.appendChild(li);
    }

    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>`;
    pagination.appendChild(nextLi);

    // Add click events
    pagination.addEventListener('click', function(e) {
        e.preventDefault();
        if (e.target.classList.contains('page-link') && !e.target.parentNode.classList.contains('disabled')) {
            const page = parseInt(e.target.dataset.page);
            if (page >= 1 && page <= totalPages) {
                showPageCallback(page);
            }
        }
    });
}

function updateTableInfo(tableId) {
    const tableInfo = document.getElementById(`${tableId}TableInfo`);
    if (!tableInfo) return;

    const table = document.getElementById(tableId);
    const visibleRows = table.querySelectorAll('tbody tr[style=""], tbody tr:not([style])');
    const totalRows = table.querySelectorAll('tbody tr').length;

    tableInfo.textContent = `Showing ${visibleRows.length} of ${totalRows} entries`;
}

function confirmDelete(commodityId, commodityName) {
    document.getElementById('commodityName').textContent = commodityName;
    document.getElementById('deleteForm').action = '<?= base_url('admin/commodities') ?>/' + commodityId + '/delete';

    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
<?= $this->endSection() ?>
