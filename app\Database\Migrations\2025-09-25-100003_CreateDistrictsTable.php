<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateDistrictsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'SERIAL',
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'province_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => false,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'code' => [
                'type' => 'VARCHAR',
                'constraint' => 10,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('province_id');
        $this->forge->createTable('districts');

        // Set default timestamp
        $this->db->query('ALTER TABLE districts ALTER COLUMN created_at SET DEFAULT CURRENT_TIMESTAMP');

        // Add indexes for performance
        $this->db->query('CREATE INDEX idx_districts_province_id ON districts(province_id)');
        $this->db->query('CREATE INDEX idx_districts_name ON districts(name)');

        // Add foreign key constraint
        if ($this->db->DBDriver === 'Postgre') {
            $this->db->query('ALTER TABLE districts ADD CONSTRAINT fk_districts_province FOREIGN KEY (province_id) REFERENCES provinces(id) ON DELETE CASCADE');
        }
    }

    public function down()
    {
        // Drop foreign key constraint first
        if ($this->db->DBDriver === 'Postgre') {
            $this->db->query('ALTER TABLE districts DROP CONSTRAINT IF EXISTS fk_districts_province');
        }
        
        $this->forge->dropTable('districts');
    }
}
