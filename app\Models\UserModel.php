<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'fullname',
        'username',
        'email',
        'password_hash',
        'is_admin',
        'is_buyer',
        'is_supervisor',
        'reports_to',
        'status',
        'remarks',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    protected $validationRules = [
        'fullname' => 'required|min_length[2]|max_length[255]',
        'username' => 'required|min_length[3]|max_length[50]|is_unique[users.username]',
        'email' => 'required|valid_email|is_unique[users.email]',
        'password_hash' => 'required|min_length[8]',
        'status' => 'required|in_list[active,inactive,suspended,pending]'
    ];

    protected $validationMessages = [
        'fullname' => [
            'required' => 'Full name is required',
            'min_length' => 'Full name must be at least 2 characters long'
        ],
        'username' => [
            'required' => 'Username is required',
            'min_length' => 'Username must be at least 3 characters long',
            'is_unique' => 'This username is already taken'
        ],
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Please provide a valid email address',
            'is_unique' => 'This email is already registered'
        ],
        'password_hash' => [
            'required' => 'Password is required',
            'min_length' => 'Password must be at least 8 characters long'
        ]
    ];

    /**
     * Find user by email
     */
    public function findByEmail(string $email)
    {
        return $this->where('LOWER(email)', strtolower($email))->first();
    }

    /**
     * Find user by username
     */
    public function findByUsername(string $username)
    {
        return $this->where('LOWER(username)', strtolower($username))->first();
    }

    /**
     * Find user by ID
     */
    public function findById(int $id)
    {
        return $this->find($id);
    }

    /**
     * Create a new user
     */
    public function createUser(array $data, ?int $createdBy = null)
    {
        // Hash password before storing
        if (isset($data['password'])) {
            $data['password_hash'] = password_hash($data['password'], PASSWORD_DEFAULT);
            unset($data['password']); // Remove plain password
        }

        // Set default values
        $data['status'] = $data['status'] ?? 'active';
        $data['is_buyer'] = $data['is_buyer'] ?? true;
        $data['is_admin'] = $data['is_admin'] ?? false;
        $data['is_supervisor'] = $data['is_supervisor'] ?? false;

        if ($createdBy) {
            $data['created_by'] = $createdBy;
        }

        return $this->insert($data);
    }

    /**
     * Update user data
     */
    public function updateUser(int $id, array $data, ?int $updatedBy = null)
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password_hash'] = password_hash($data['password'], PASSWORD_DEFAULT);
            unset($data['password']); // Remove plain password
        }

        if ($updatedBy) {
            $data['updated_by'] = $updatedBy;
        }

        // Temporarily disable validation for updates since we validate in controller
        $this->skipValidation = true;
        $result = $this->update($id, $data);
        $this->skipValidation = false;

        return $result;
    }

    /**
     * Verify user password
     */
    public function verifyPassword(string $password, string $hashedPassword): bool
    {
        return password_verify($password, $hashedPassword);
    }

    /**
     * Override find to normalize boolean fields
     */
    public function find($id = null)
    {
        $result = parent::find($id);
        return $this->normalizeBooleanFields($result);
    }

    /**
     * Override findAll to normalize boolean fields
     */
    public function findAll(?int $limit = null, int $offset = 0)
    {
        $result = parent::findAll($limit, $offset);
        return $this->normalizeBooleanFields($result);
    }

    /**
     * Get all users with pagination
     */
    public function getAllUsers(int $limit = 20, int $offset = 0, array $filters = [])
    {
        $builder = $this->orderBy('created_at', 'DESC');

        if (!empty($filters)) {
            foreach ($filters as $field => $value) {
                $builder->where($field, $value);
            }
        }

        $users = $builder->findAll($limit, $offset);
        return $this->normalizeBooleanFields($users);
    }

    /**
     * Get admin users
     */
    public function getAdminUsers()
    {
        return $this->where('is_admin', true)->where('status', 'active')->findAll();
    }

    /**
     * Get buyer users
     */
    public function getBuyerUsers()
    {
        return $this->where('is_buyer', true)->where('status', 'active')->findAll();
    }

    /**
     * Get supervisor users
     */
    public function getSupervisorUsers()
    {
        return $this->where('is_supervisor', true)->where('status', 'active')->findAll();
    }

    /**
     * Get active users
     */
    public function getActiveUsers()
    {
        return $this->where('status', 'active')->findAll();
    }

    /**
     * Deactivate user
     */
    public function deactivateUser(int $id, ?int $updatedBy = null)
    {
        return $this->updateUser($id, ['status' => 'inactive'], $updatedBy);
    }

    /**
     * Activate user
     */
    public function activateUser(int $id, ?int $updatedBy = null)
    {
        return $this->updateUser($id, ['status' => 'active'], $updatedBy);
    }

    /**
     * Delete user (soft delete)
     */
    public function deleteUser(int $id, ?int $deletedBy = null)
    {
        $data = [];
        if ($deletedBy) {
            $data['deleted_by'] = $deletedBy;
        }
        return $this->delete($id, false); // Soft delete
    }

    /**
     * Check if email exists
     */
    public function emailExists(string $email): bool
    {
        $user = $this->findByEmail($email);
        return $user !== null;
    }

    /**
     * Check if username exists
     */
    public function usernameExists(string $username): bool
    {
        $user = $this->findByUsername($username);
        return $user !== null;
    }

    /**
     * Get user statistics
     */
    public function getUserStats()
    {
        // Get total users (excluding soft deleted)
        $totalUsers = $this->countAllResults();

        // Get active users
        $activeUsers = $this->where('status', 'active')->countAllResults(false);

        // Get users by role (active only)
        $admins = $this->where('is_admin', true)->where('status', 'active')->countAllResults(false);
        $buyers = $this->where('is_buyer', true)->where('status', 'active')->countAllResults(false);
        $supervisors = $this->where('is_supervisor', true)->where('status', 'active')->countAllResults(false);

        // Get users by status
        $inactive = $this->where('status', 'inactive')->countAllResults(false);
        $suspended = $this->where('status', 'suspended')->countAllResults(false);
        $pending = $this->where('status', 'pending')->countAllResults(false);

        return [
            'total' => $totalUsers,
            'active' => $activeUsers,
            'inactive' => $inactive,
            'suspended' => $suspended,
            'pending' => $pending,
            'admins' => $admins,
            'buyers' => $buyers,
            'supervisors' => $supervisors
        ];
    }

    /**
     * Get users with their supervisor information
     */
    public function getUsersWithSupervisor(int $limit = 20, int $offset = 0)
    {
        return $this->select('users.*, supervisor.fullname as supervisor_name')
                   ->join('users as supervisor', 'users.reports_to = supervisor.id', 'left')
                   ->orderBy('users.created_at', 'DESC')
                   ->findAll($limit, $offset);
    }

    /**
     * Get subordinates of a supervisor
     */
    public function getSubordinates(int $supervisorId)
    {
        return $this->where('reports_to', $supervisorId)
                   ->where('status', 'active')
                   ->orderBy('fullname', 'ASC')
                   ->findAll();
    }

    /**
     * Bulk update user status
     */
    public function bulkUpdateStatus(array $userIds, string $status, ?int $updatedBy = null)
    {
        $data = ['status' => $status];
        if ($updatedBy) {
            $data['updated_by'] = $updatedBy;
        }

        $count = 0;
        foreach ($userIds as $id) {
            if ($this->update($id, $data)) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Bulk delete users
     */
    public function bulkDelete(array $userIds, ?int $deletedBy = null)
    {
        $count = 0;
        foreach ($userIds as $id) {
            if ($this->deleteUser($id, $deletedBy)) {
                $count++;
            }
        }

        return $count;
    }

    /**
     * Get filtered users for export
     */
    public function getFilteredUsers($search = '', $roleFilter = '', $statusFilter = '')
    {
        $builder = $this->builder();

        // Apply search filter
        if (!empty($search)) {
            $builder->groupStart()
                    ->like('fullname', $search)
                    ->orLike('username', $search)
                    ->orLike('email', $search)
                    ->groupEnd();
        }

        // Apply role filter
        if (!empty($roleFilter)) {
            switch ($roleFilter) {
                case 'admin':
                    $builder->where('is_admin', 1);
                    break;
                case 'supervisor':
                    $builder->where('is_supervisor', 1);
                    break;
                case 'buyer':
                    $builder->where('is_buyer', 1);
                    break;
            }
        }

        // Apply status filter
        if (!empty($statusFilter)) {
            $builder->where('status', $statusFilter);
        }

        $users = $builder->orderBy('created_at', 'DESC')->get()->getResultArray();
        return $this->normalizeBooleanFields($users);
    }

    /**
     * Normalize boolean fields from database
     * PostgreSQL returns boolean values that might need conversion
     */
    private function normalizeBooleanFields($users)
    {
        if (is_array($users) && isset($users[0])) {
            // Multiple users
            foreach ($users as &$user) {
                $user = $this->normalizeSingleUserBooleans($user);
            }
        } elseif (is_array($users)) {
            // Single user
            $users = $this->normalizeSingleUserBooleans($users);
        }

        return $users;
    }

    /**
     * Normalize boolean fields for a single user
     */
    private function normalizeSingleUserBooleans($user)
    {
        if (is_array($user)) {
            // Handle PostgreSQL boolean values properly
            $user['is_admin'] = $this->normalizeBoolean($user['is_admin']);
            $user['is_supervisor'] = $this->normalizeBoolean($user['is_supervisor']);
            $user['is_buyer'] = $this->normalizeBoolean($user['is_buyer']);
        }

        return $user;
    }

    /**
     * Normalize a single boolean value
     */
    private function normalizeBoolean($value)
    {
        // Handle various boolean representations
        if (is_bool($value)) {
            return $value;
        }

        if (is_string($value)) {
            $value = strtolower(trim($value));
            return in_array($value, ['true', '1', 'yes', 'on', 't'], true);
        }

        if (is_numeric($value)) {
            return (bool) $value;
        }

        return false;
    }
}
