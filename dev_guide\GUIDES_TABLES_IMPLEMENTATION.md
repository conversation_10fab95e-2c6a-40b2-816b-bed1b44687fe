# Enhanced Tables Implementation Guide

A comprehensive guide for implementing enhanced tables with search, pagination, and Excel export functionality using vanilla JavaScript that follows the existing DCBuyer codebase architecture.

## Overview

The enhanced table system provides:
- **Real-time search functionality** across all table content
- **Client-side pagination** for large datasets
- **Professional Excel export** (.xlsx format with formatting)
- **Responsive design** that works on all devices
- **No external dependencies** (pure vanilla JavaScript)
- **Consistent styling** with existing admin template patterns

## Quick Start

### 1. Basic HTML Structure

```html
<div class="table-responsive">
    <table id="yourTableId" class="table table-hover mb-0">
        <thead>
            <tr>
                <th>Column 1</th>
                <th>Column 2</th>
                <th>Column 3</th>
                <th>Actions</th> <!-- Always place Actions column last -->
            </tr>
        </thead>
        <tbody>
            <!-- Your table rows here -->
        </tbody>
    </table>
</div>
```

### 2. Add Required CSS and Library

```php
<?= $this->section('styles') ?>
<!-- SheetJS for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<style>
    /* Custom Table Controls Styling */
    .table-controls {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-controls input[type="text"] {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 0.5rem;
    }

    .table-controls input[type="text"]:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .table-pagination {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-info {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .pagination .page-link {
        border-radius: 8px;
        margin: 0 2px;
        border: 1px solid #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
    }
</style>
<?= $this->endSection() ?>
```

### 3. Initialize the Enhanced Table

```php
<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedTable('yourTableId', {
        searchPlaceholder: 'Search your data...',
        exportFilename: 'your_data_export',
        rowsPerPage: 25
    });
});
</script>
<?= $this->endSection() ?>
```

## Complete JavaScript Implementation

Add this complete JavaScript code to your view's scripts section:

```javascript
function initializeEnhancedTable(tableId, options = {}) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const config = {
        searchPlaceholder: options.searchPlaceholder || 'Search...',
        exportFilename: options.exportFilename || 'table_export',
        rowsPerPage: options.rowsPerPage || 25,
        ...options
    };

    // Add search functionality
    addTableSearch(tableId, config);

    // Add export functionality
    addExportButton(tableId, config);

    // Add pagination
    addTablePagination(tableId, config);

    // Initialize existing admin template table features
    AdminTemplate.initializeDataTable(tableId, {
        sortable: true,
        searchable: true
    });
}

function addTableSearch(tableId, config) {
    const table = document.getElementById(tableId);
    const tableContainer = table.closest('.table-responsive').parentNode;

    // Create search container
    const searchContainer = document.createElement('div');
    searchContainer.className = 'table-controls mb-3 d-flex justify-content-between align-items-center';

    // Create search input
    const searchDiv = document.createElement('div');
    searchDiv.className = 'd-flex align-items-center';
    searchDiv.innerHTML = \`
        <label class="me-2 mb-0">Search:</label>
        <input type="text" id="\${tableId}Search" class="form-control" style="width: 300px;" placeholder="\${config.searchPlaceholder}">
    \`;

    // Create export button
    const exportDiv = document.createElement('div');
    exportDiv.innerHTML = \`
        <button type="button" id="\${tableId}ExportExcel" class="btn btn-success">
            <i class="fas fa-file-excel me-2"></i>Export to Excel
        </button>
    \`;

    searchContainer.appendChild(searchDiv);
    searchContainer.appendChild(exportDiv);
    tableContainer.insertBefore(searchContainer, tableContainer.firstChild);

    // Add search functionality
    const searchInput = document.getElementById(\`\${tableId}Search\`);
    searchInput.addEventListener('input', function() {
        filterTable(tableId, this.value.toLowerCase());
    });
}

function filterTable(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    updateTableInfo(tableId);
}

function addExportButton(tableId, config) {
    const exportBtn = document.getElementById(\`\${tableId}ExportExcel\`);
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportTableToExcel(tableId, config.exportFilename);
        });
    }
}

function exportTableToExcel(tableId, filename) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tr');
    const data = [];

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll(index === 0 ? 'th' : 'td');
        const rowData = [];

        cells.forEach((cell, cellIndex) => {
            // Skip the Actions column (last column)
            if (cellIndex < cells.length - 1) {
                let cellText = cell.textContent.trim();
                // Clean up status badges
                if (cell.querySelector('.badge')) {
                    cellText = cell.querySelector('.badge').textContent.trim();
                }
                // Clean up currency formatting for numbers
                if (cellText.startsWith('PGK')) {
                    const numValue = parseFloat(cellText.replace(/[PGK,]/g, ''));
                    rowData.push(isNaN(numValue) ? cellText : numValue);
                } else {
                    rowData.push(cellText);
                }
            }
        });

        if (rowData.length > 0) {
            data.push(rowData);
        }
    });

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(data);

    // Set column widths (adjust as needed for your table)
    const colWidths = data[0].map(() => ({ wch: 15 }));
    ws['!cols'] = colWidths;

    // Style the header row
    const headerRange = XLSX.utils.decode_range(ws['!ref']);
    for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!ws[cellAddress]) continue;
        ws[cellAddress].s = {
            font: { bold: true, color: { rgb: "FFFFFF" } },
            fill: { fgColor: { rgb: "2E7D32" } },
            alignment: { horizontal: "center" }
        };
    }

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, "Data Export");

    // Generate filename with current date
    const exportFilename = filename + '_' + new Date().toISOString().split('T')[0] + '.xlsx';

    // Save the file
    XLSX.writeFile(wb, exportFilename);

    AdminTemplate.showNotification('Excel file exported successfully!', 'success');
}

function addTablePagination(tableId, config) {
    const table = document.getElementById(tableId);
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    if (rows.length <= config.rowsPerPage) return; // No pagination needed for small tables

    let currentPage = 1;
    const rowsPerPage = config.rowsPerPage;
    const totalPages = Math.ceil(rows.length / rowsPerPage);

    // Create pagination container
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'table-pagination mt-3 d-flex justify-content-between align-items-center';
    paginationContainer.innerHTML = \`
        <div class="table-info">
            <span id="\${tableId}TableInfo">Showing 1 to \${Math.min(rowsPerPage, rows.length)} of \${rows.length} entries</span>
        </div>
        <nav>
            <ul class="pagination mb-0" id="\${tableId}TablePagination">
                <!-- Pagination buttons will be generated here -->
            </ul>
        </nav>
    \`;

    table.parentNode.appendChild(paginationContainer);

    function showPage(page) {
        const start = (page - 1) * rowsPerPage;
        const end = start + rowsPerPage;

        rows.forEach((row, index) => {
            if (index >= start && index < end && row.style.display !== 'none') {
                row.style.display = '';
            } else if (row.style.display !== 'none') {
                row.style.display = 'none';
            }
        });

        updatePagination(tableId, page, totalPages, showPage);
        updateTableInfo(tableId);
    }

    // Initialize first page
    showPage(1);
}

function updatePagination(tableId, currentPage, totalPages, showPageCallback) {
    const pagination = document.getElementById(\`\${tableId}TablePagination\`);
    pagination.innerHTML = '';

    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = \`page-item \${currentPage === 1 ? 'disabled' : ''}\`;
    prevLi.innerHTML = \`<a class="page-link" href="#" data-page="\${currentPage - 1}">Previous</a>\`;
    pagination.appendChild(prevLi);

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = \`page-item \${i === currentPage ? 'active' : ''}\`;
        li.innerHTML = \`<a class="page-link" href="#" data-page="\${i}">\${i}</a>\`;
        pagination.appendChild(li);
    }

    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = \`page-item \${currentPage === totalPages ? 'disabled' : ''}\`;
    nextLi.innerHTML = \`<a class="page-link" href="#" data-page="\${currentPage + 1}">Next</a>\`;
    pagination.appendChild(nextLi);

    // Add click events
    pagination.addEventListener('click', function(e) {
        e.preventDefault();
        if (e.target.classList.contains('page-link') && !e.target.parentNode.classList.contains('disabled')) {
            const page = parseInt(e.target.dataset.page);
            if (page >= 1 && page <= totalPages) {
                showPageCallback(page);
            }
        }
    });
}

function updateTableInfo(tableId) {
    const tableInfo = document.getElementById(\`\${tableId}TableInfo\`);
    if (!tableInfo) return;

    const table = document.getElementById(tableId);
    const visibleRows = table.querySelectorAll('tbody tr[style=""], tbody tr:not([style])');
    const totalRows = table.querySelectorAll('tbody tr').length;

    tableInfo.textContent = \`Showing \${visibleRows.length} of \${totalRows} entries\`;
}
```

## Usage Examples

### Basic Implementation
```javascript
// Simple initialization with defaults
initializeEnhancedTable('myTable');
```

### Custom Configuration
```javascript
// Custom settings for specific needs
initializeEnhancedTable('usersTable', {
    searchPlaceholder: 'Search users by name, email, or role...',
    exportFilename: 'users_report',
    rowsPerPage: 50
});
```

### Multiple Tables
```javascript
// Different tables on the same page
initializeEnhancedTable('usersTable', {
    searchPlaceholder: 'Search users...',
    exportFilename: 'users_report'
});

initializeEnhancedTable('ordersTable', {
    searchPlaceholder: 'Search orders...',
    exportFilename: 'orders_report',
    rowsPerPage: 10
});
```

## Features

### 🔍 Search Functionality
- **Real-time filtering**: Instant results as you type
- **Case-insensitive**: Searches all text content
- **Cross-column search**: Searches across all table columns
- **Custom placeholder**: Configurable search hint text

### 📊 Excel Export
- **Professional formatting**: Green headers (#2E7D32) with white text
- **Real .xlsx files**: Not CSV - proper Excel format
- **Smart data handling**: Currency values exported as numbers for calculations
- **Date-stamped filenames**: Automatic date inclusion (YYYY-MM-DD)
- **Actions column exclusion**: Automatically skips Actions column
- **Custom column widths**: Optimized for readability

### 📄 Pagination
- **Configurable page size**: Default 25 rows, customizable
- **Smart activation**: Only appears when needed (>25 rows)
- **Full navigation**: Previous/Next + page numbers
- **Entry counter**: Shows "X of Y entries" information
- **Search-aware**: Updates based on filtered results

### 🔄 Sorting
- **Existing integration**: Uses AdminTemplate.initializeDataTable()
- **Clickable headers**: Click any column header to sort
- **Visual feedback**: Sort direction indicators
- **Consistent behavior**: Follows existing codebase patterns

## Customization

### Column Widths for Excel Export
```javascript
// Modify in exportTableToExcel function
const colWidths = [
    { wch: 12 }, // Column 1: 12 characters
    { wch: 35 }, // Column 2: 35 characters (wider for names)
    { wch: 15 }, // Column 3: 15 characters
    { wch: 20 }  // Column 4: 20 characters
];
```

### Custom Search Behavior
```javascript
function filterTable(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        // Custom search logic - example: search only specific columns
        const nameCell = row.querySelector('td:nth-child(2)'); // Second column
        const emailCell = row.querySelector('td:nth-child(3)'); // Third column

        const searchText = (nameCell?.textContent || '') + ' ' + (emailCell?.textContent || '');
        const matches = searchText.toLowerCase().includes(searchTerm);

        row.style.display = matches ? '' : 'none';
    });

    updateTableInfo(tableId);
}
```

### Custom Styling
```css
/* Override default styles */
.table-controls {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

.table-controls .btn-success {
    background: #28a745;
    border-color: #28a745;
}

.table-pagination {
    background: #f8f9fa;
}
```

## Best Practices

### 1. Table Structure
- Always use unique table IDs
- Place Actions column last (will be excluded from export)
- Use semantic HTML structure with proper thead/tbody

### 2. Performance
- Use client-side processing for <1000 rows
- Consider server-side pagination for larger datasets
- Test with realistic data volumes

### 3. Accessibility
- Include proper labels for search inputs
- Use semantic HTML elements
- Test with keyboard navigation

### 4. Responsive Design
- Test on mobile devices
- Ensure search input is appropriately sized
- Verify pagination works on small screens

## Browser Support

- **Chrome**: 60+
- **Firefox**: 60+
- **Safari**: 12+
- **Edge**: 79+
- **Mobile**: iOS Safari 12+, Chrome Mobile 60+

## Dependencies

- **SheetJS**: Excel export functionality (CDN)
- **Bootstrap 5**: Styling (included in admin template)
- **AdminTemplate**: Existing admin functions
- **No jQuery**: Pure vanilla JavaScript

## Troubleshooting

### Common Issues

**Table not initializing**
```javascript
// Check console for errors
console.log(document.getElementById('yourTableId')); // Should not be null
```

**Search not working**
- Verify table has tbody element
- Check for JavaScript errors in console
- Ensure unique table ID

**Export not working**
- Verify SheetJS library is loaded
- Check browser console for errors
- Ensure table has data

**Pagination not appearing**
- Check if table has more rows than configured page size
- Verify table structure is correct

### Debug Mode
```javascript
function initializeEnhancedTable(tableId, options = {}) {
    console.log(`Initializing table: ${tableId}`, options);
    const table = document.getElementById(tableId);
    if (!table) {
        console.error(`Table '${tableId}' not found`);
        return;
    }
    // ... rest of function
}
```

This implementation provides a complete, reusable table enhancement system that can be applied to any table in your DCBuyer application while maintaining consistency with the existing codebase architecture.
- **Pagination**: Automatic pagination for tables with >25 rows
- **Performance**: Client-side processing for instant results
- **Architecture**: Pure vanilla JavaScript following existing codebase patterns

### Browser Compatibility
- **Modern Browsers**: Chrome 60+, Firefox 60+, Safari 12+, Edge 79+
- **Mobile Responsive**: Works on all device sizes
- **No Dependencies**: No jQuery or external frameworks required
