<?php

namespace App\Libraries;

use CodeIgniter\Cache\CacheInterface;

/**
 * Cache Helper Library
 * 
 * Provides convenient methods for caching database queries and other data
 * to improve application performance.
 */
class CacheHelper
{
    private CacheInterface $cache;
    private string $prefix;
    
    public function __construct()
    {
        $this->cache = \Config\Services::cache();
        $this->prefix = 'dcbuyer_';
    }
    
    /**
     * Remember a value in cache, executing callback if not found
     * 
     * @param string $key Cache key
     * @param int $ttl Time to live in seconds
     * @param callable $callback Function to execute if cache miss
     * @return mixed Cached or computed value
     */
    public function remember(string $key, int $ttl, callable $callback)
    {
        $fullKey = $this->prefix . $key;
        $data = $this->cache->get($fullKey);
        
        if ($data === null) {
            $data = $callback();
            if ($data !== null) {
                $this->cache->save($fullKey, $data, $ttl);
            }
        }
        
        return $data;
    }
    
    /**
     * Store a value in cache
     * 
     * @param string $key Cache key
     * @param mixed $value Value to cache
     * @param int $ttl Time to live in seconds
     * @return bool Success status
     */
    public function put(string $key, $value, int $ttl = 300): bool
    {
        $fullKey = $this->prefix . $key;
        return $this->cache->save($fullKey, $value, $ttl);
    }
    
    /**
     * Get a value from cache
     * 
     * @param string $key Cache key
     * @param mixed $default Default value if not found
     * @return mixed Cached value or default
     */
    public function get(string $key, $default = null)
    {
        $fullKey = $this->prefix . $key;
        $value = $this->cache->get($fullKey);
        return $value !== null ? $value : $default;
    }
    
    /**
     * Remove a value from cache
     * 
     * @param string $key Cache key
     * @return bool Success status
     */
    public function forget(string $key): bool
    {
        $fullKey = $this->prefix . $key;
        return $this->cache->delete($fullKey);
    }
    
    /**
     * Clear all cache with our prefix
     * 
     * @return bool Success status
     */
    public function flush(): bool
    {
        return $this->cache->clean();
    }
    
    /**
     * Generate cache key for paginated queries
     * 
     * @param string $base Base key name
     * @param array $params Parameters to include in key
     * @return string Generated cache key
     */
    public function generatePaginationKey(string $base, array $params = []): string
    {
        $keyParts = [$base];
        
        foreach ($params as $key => $value) {
            if (is_array($value)) {
                $value = md5(serialize($value));
            }
            $keyParts[] = $key . '_' . $value;
        }
        
        return implode('_', $keyParts);
    }
    
    /**
     * Cache database query results with automatic key generation
     * 
     * @param string $model Model name
     * @param string $method Method name
     * @param array $params Method parameters
     * @param int $ttl Time to live in seconds
     * @param callable $callback Query callback
     * @return mixed Query results
     */
    public function cacheQuery(string $model, string $method, array $params, int $ttl, callable $callback)
    {
        $key = $this->generateQueryKey($model, $method, $params);
        return $this->remember($key, $ttl, $callback);
    }
    
    /**
     * Generate cache key for database queries
     * 
     * @param string $model Model name
     * @param string $method Method name
     * @param array $params Method parameters
     * @return string Generated cache key
     */
    private function generateQueryKey(string $model, string $method, array $params): string
    {
        $paramHash = md5(serialize($params));
        return strtolower($model) . '_' . $method . '_' . $paramHash;
    }
    
    /**
     * Invalidate cache for a specific model
     * 
     * @param string $model Model name
     * @return bool Success status
     */
    public function invalidateModel(string $model): bool
    {
        // Note: This is a simple implementation
        // For more sophisticated cache invalidation, consider using cache tags
        return $this->flush();
    }
}
