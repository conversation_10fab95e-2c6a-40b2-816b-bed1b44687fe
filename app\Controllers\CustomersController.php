<?php

namespace App\Controllers;

use App\Models\CustomerModel;
use App\Models\LocationModel;

class CustomersController extends BaseController
{
    protected $customerModel;
    protected $locationModel;
    protected $data = [];

    public function __construct()
    {
        $this->customerModel = new CustomerModel();
        $this->locationModel = new LocationModel();

        // Load helpers
        helper(['form', 'url']);

        // Initialize common admin data
        $this->data['title'] = 'Customer Management - DCBuyer Admin';
        $this->data['active_menu'] = 'customers';
        $this->data['user_name'] = session('fullname') ?? session('username') ?? 'User';
        $this->data['user_email'] = session('email') ?? '<EMAIL>';
        $this->data['user_role'] = session('is_admin') ? 'administrator' : (session('is_supervisor') ? 'supervisor' : 'user');
    }

    /**
     * Display list of customers (GET)
     */
    public function index()
    {
        $search = $this->request->getGet('search') ?? '';
        $status = $this->request->getGet('status') ?? '';
        $locationId = $this->request->getGet('location_id') ?? '';
        $perPage = 20;

        // Build filters array
        $filters = array_filter([
            'search' => $search,
            'status' => $status,
            'location_id' => $locationId
        ]);

        // Get customers with pagination and filters
        $customers = $this->customerModel->getCustomersWithDetails($perPage, $filters);
        $pager = $this->customerModel->pager;

        // Get customer statistics
        $stats = $this->customerModel->getCustomerStats();

        // Get filter options
        $locations = $this->locationModel->getLocationsForDropdown();

        $this->data['customers'] = $customers;
        $this->data['pager'] = $pager;
        $this->data['stats'] = $stats;
        $this->data['locations'] = $locations;
        $this->data['filters'] = $filters;

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Customers', 'url' => base_url('admin/customers')]
        ];

        $this->data['page_title'] = 'Customer Management';
        $this->data['page_description'] = 'Manage customer information and contact details.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/customers/create') . '" class="btn btn-admin-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Add New Customer
            </a>
        ';

        return view('admin/customers/customers_index', $this->data);
    }

    /**
     * Show create customer form (GET)
     */
    public function create()
    {
        // Get locations for dropdown
        $locations = $this->locationModel->getLocationsForDropdown();

        $this->data['customer'] = [
            'first_name' => old('first_name'),
            'last_name' => old('last_name'),
            'phone' => old('phone'),
            'email' => old('email'),
            'address' => old('address'),
            'location_id' => old('location_id'),
            'status' => old('status', 'active')
        ];

        $this->data['locations'] = $locations;

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Customers', 'url' => base_url('admin/customers')],
            ['title' => 'Create Customer', 'url' => base_url('admin/customers/create')]
        ];

        $this->data['page_title'] = 'Create New Customer';
        $this->data['page_description'] = 'Add a new customer to the system.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/customers') . '" class="btn btn-secondary btn-lg">
                <i class="fas fa-arrow-left me-2"></i>Back to Customers
            </a>
        ';

        return view('admin/customers/customers_create', $this->data);
    }

    /**
     * Store new customer (POST)
     */
    public function store()
    {
        // Get current user ID from session
        $currentUserId = session()->get('user_id');

        if (!$currentUserId) {
            return redirect()->to('login')->with('error', 'Please log in to create customers.');
        }

        // Validation rules
        $rules = [
            'first_name' => 'required|max_length[50]',
            'last_name' => 'required|max_length[50]',
            'phone' => 'permit_empty|max_length[20]',
            'email' => 'permit_empty|valid_email|max_length[100]|is_unique[customers.email]',
            'address' => 'permit_empty',
            'location_id' => 'permit_empty|integer',
            'status' => 'required|in_list[active,inactive]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'phone' => $this->request->getPost('phone'),
            'email' => $this->request->getPost('email'),
            'address' => $this->request->getPost('address'),
            'location_id' => $this->request->getPost('location_id') ?: null,
            'status' => $this->request->getPost('status'),
            'created_by' => $currentUserId,
            'updated_by' => $currentUserId
        ];

        try {
            $result = $this->customerModel->createCustomer($data, $currentUserId);

            if ($result) {
                return redirect()->to('admin/customers')->with('success', 'Customer created successfully.');
            } else {
                $errors = $this->customerModel->errors();
                return redirect()->back()->withInput()->with('error', 'Failed to create customer. ' . implode(', ', $errors));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating customer: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while creating the customer: ' . $e->getMessage());
        }
    }

    /**
     * Show customer details (GET)
     */
    public function show($id)
    {
        $customer = $this->customerModel->getCustomersWithDetails(1, ['id' => $id]);

        if (empty($customer)) {
            return redirect()->to('admin/customers')->with('error', 'Customer not found.');
        }

        $customer = $customer[0];

        // Get customer transaction history
        $transactions = $this->customerModel->getCustomerTransactions($id, 10);

        $this->data['customer'] = $customer;
        $this->data['transactions'] = $transactions;

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Customers', 'url' => base_url('admin/customers')],
            ['title' => 'Customer Details', 'url' => base_url('admin/customers/' . $id)]
        ];

        $this->data['page_title'] = 'Customer Details';
        $this->data['page_description'] = 'View customer information and transaction history.';
        $this->data['page_actions'] = '
            <div class="btn-group">
                <a href="' . base_url('admin/customers/' . $id . '/edit') . '" class="btn btn-admin-primary btn-lg">
                    <i class="fas fa-edit me-2"></i>Edit Customer
                </a>
                <a href="' . base_url('admin/customers') . '" class="btn btn-secondary btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>Back to List
                </a>
            </div>
        ';

        return view('admin/customers/customers_show', $this->data);
    }

    /**
     * Show edit customer form (GET)
     */
    public function edit($id)
    {
        $customer = $this->customerModel->find($id);

        if (!$customer) {
            return redirect()->to('admin/customers')->with('error', 'Customer not found.');
        }

        // Get locations for dropdown
        $locations = $this->locationModel->getLocationsForDropdown();

        $this->data['customer'] = $customer;
        $this->data['locations'] = $locations;

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Customers', 'url' => base_url('admin/customers')],
            ['title' => 'Edit Customer', 'url' => base_url('admin/customers/' . $id . '/edit')]
        ];

        $this->data['page_title'] = 'Edit Customer';
        $this->data['page_description'] = 'Modify customer information and details.';
        $this->data['page_actions'] = '
            <div class="btn-group">
                <a href="' . base_url('admin/customers/' . $id) . '" class="btn btn-info btn-lg">
                    <i class="fas fa-eye me-2"></i>View Details
                </a>
                <a href="' . base_url('admin/customers') . '" class="btn btn-secondary btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>Back to List
                </a>
            </div>
        ';

        return view('admin/customers/customers_edit', $this->data);
    }

    /**
     * Update customer (PUT)
     */
    public function update($id)
    {
        $customer = $this->customerModel->find($id);

        if (!$customer) {
            return redirect()->to('admin/customers')->with('error', 'Customer not found.');
        }

        // Get current user ID from session
        $currentUserId = session()->get('user_id');

        if (!$currentUserId) {
            return redirect()->to('login')->with('error', 'Please log in to update customers.');
        }

        // Validation rules (email uniqueness check excludes current customer)
        $rules = [
            'first_name' => 'required|max_length[50]',
            'last_name' => 'required|max_length[50]',
            'phone' => 'permit_empty|max_length[20]',
            'email' => 'permit_empty|valid_email|max_length[100]|is_unique[customers.email,id,' . $id . ']',
            'address' => 'permit_empty',
            'location_id' => 'permit_empty|integer',
            'status' => 'required|in_list[active,inactive]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'first_name' => $this->request->getPost('first_name'),
            'last_name' => $this->request->getPost('last_name'),
            'phone' => $this->request->getPost('phone'),
            'email' => $this->request->getPost('email'),
            'address' => $this->request->getPost('address'),
            'location_id' => $this->request->getPost('location_id') ?: null,
            'status' => $this->request->getPost('status'),
            'updated_by' => $currentUserId
        ];

        try {
            $result = $this->customerModel->updateCustomer($id, $data, $currentUserId);

            if ($result) {
                return redirect()->to('admin/customers')->with('success', 'Customer updated successfully.');
            } else {
                $errors = $this->customerModel->errors();
                return redirect()->back()->withInput()->with('error', 'Failed to update customer. ' . implode(', ', $errors));
            }
        } catch (\Exception $e) {
            log_message('error', 'Error updating customer: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating the customer.');
        }
    }

    /**
     * Delete customer (DELETE)
     */
    public function delete($id)
    {
        $customer = $this->customerModel->find($id);

        if (!$customer) {
            return redirect()->to('admin/customers')->with('error', 'Customer not found.');
        }

        // Check if customer has any transactions
        $transactions = $this->customerModel->getCustomerTransactions($id, 1);
        if (!empty($transactions)) {
            return redirect()->to('admin/customers')->with('error', 'Cannot delete customer with existing transactions. Please deactivate instead.');
        }

        try {
            $result = $this->customerModel->delete($id);

            if ($result) {
                return redirect()->to('admin/customers')->with('success', 'Customer deleted successfully.');
            } else {
                return redirect()->to('admin/customers')->with('error', 'Failed to delete customer.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error deleting customer: ' . $e->getMessage());
            return redirect()->to('admin/customers')->with('error', 'An error occurred while deleting the customer.');
        }
    }

    /**
     * Activate customer (PATCH)
     */
    public function activate($id)
    {
        $customer = $this->customerModel->find($id);

        if (!$customer) {
            return redirect()->to('admin/customers')->with('error', 'Customer not found.');
        }

        try {
            $result = $this->customerModel->activateCustomer($id, session()->get('user_id'));

            if ($result) {
                return redirect()->to('admin/customers')->with('success', 'Customer activated successfully.');
            } else {
                return redirect()->to('admin/customers')->with('error', 'Failed to activate customer.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error activating customer: ' . $e->getMessage());
            return redirect()->to('admin/customers')->with('error', 'An error occurred while activating the customer.');
        }
    }

    /**
     * Deactivate customer (PATCH)
     */
    public function deactivate($id)
    {
        $customer = $this->customerModel->find($id);

        if (!$customer) {
            return redirect()->to('admin/customers')->with('error', 'Customer not found.');
        }

        try {
            $result = $this->customerModel->deactivateCustomer($id, session()->get('user_id'));

            if ($result) {
                return redirect()->to('admin/customers')->with('success', 'Customer deactivated successfully.');
            } else {
                return redirect()->to('admin/customers')->with('error', 'Failed to deactivate customer.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error deactivating customer: ' . $e->getMessage());
            return redirect()->to('admin/customers')->with('error', 'An error occurred while deactivating the customer.');
        }
    }

    /**
     * Search customers via AJAX (GET)
     */
    public function search()
    {
        $search = $this->request->getGet('q') ?? '';
        $limit = $this->request->getGet('limit') ?? 10;

        if (strlen($search) < 2) {
            return $this->response->setJSON([]);
        }

        $customers = $this->customerModel->searchCustomers($search, $limit);

        $results = [];
        foreach ($customers as $customer) {
            $results[] = [
                'id' => $customer['id'],
                'text' => $customer['customer_code'] . ' - ' . $customer['first_name'] . ' ' . $customer['last_name'],
                'customer_code' => $customer['customer_code'],
                'name' => $customer['first_name'] . ' ' . $customer['last_name'],
                'phone' => $customer['phone'],
                'email' => $customer['email']
            ];
        }

        return $this->response->setJSON($results);
    }
}