<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<!-- SheetJS for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<style>
    /* Custom Table Controls Styling */
    .table-controls {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-controls input[type="text"],
    .table-controls select {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 0.5rem;
    }

    .table-controls input[type="text"]:focus,
    .table-controls select:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .table-pagination {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-info {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .pagination .page-link {
        border-radius: 8px;
        margin: 0 2px;
        border: 1px solid #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
    }

    .stats-card {
        background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
        border-radius: 12px;
        padding: 1.5rem;
        color: white;
        margin-bottom: 1.5rem;
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .customer-table {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .table-header {
        background: #f8f9fa;
        padding: 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .search-box {
        max-width: 300px;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    
    .status-active {
        background: #e8f5e8;
        color: #2e7d32;
    }
    
    .status-inactive {
        background: #fff3e0;
        color: #f57c00;
    }
    
    .action-buttons .btn {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .table th {
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
    }
    
    .table td {
        vertical-align: middle;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    /* Force admin button colors */
    .btn-admin-primary,
    .btn-admin-primary:link,
    .btn-admin-primary:visited,
    .page-actions .btn-admin-primary {
        background-color: #2E7D32 !important;
        border-color: #2E7D32 !important;
        color: white !important;
    }
    
    .btn-admin-primary:hover,
    .btn-admin-primary:focus,
    .btn-admin-primary:active,
    .page-actions .btn-admin-primary:hover {
        background-color: #4CAF50 !important;
        border-color: #4CAF50 !important;
        color: white !important;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number"><?= $stats['total_customers'] ?></div>
                <div class="stats-label">Total Customers</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number"><?= $stats['active_customers'] ?></div>
                <div class="stats-label">Active Customers</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number"><?= $stats['inactive_customers'] ?></div>
                <div class="stats-label">Inactive Customers</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number"><?= $stats['recent_customers'] ?></div>
                <div class="stats-label">Recent (30 days)</div>
            </div>
        </div>
    </div>

    <!-- Customers Table -->
    <div class="customer-table">
        <div class="table-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="mb-0">Customers List</h5>
                    <small class="text-muted">Manage customer information and contacts</small>
                </div>
                <div class="col-md-6">
                    <form method="GET" class="d-flex gap-2">
                        <select name="status" class="form-select" onchange="this.form.submit()">
                            <option value="">All Status</option>
                            <option value="active" <?= ($filters['status'] ?? '') === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= ($filters['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                        <select name="location_id" class="form-select" onchange="this.form.submit()">
                            <option value="">All Locations</option>
                            <?php foreach ($locations as $location): ?>
                                <option value="<?= $location['id'] ?>" <?= ($filters['location_id'] ?? '') == $location['id'] ? 'selected' : '' ?>>
                                    <?= esc($location['location_name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <input type="text" name="search" class="form-control" placeholder="Search customers..." 
                               value="<?= esc($filters['search'] ?? '') ?>">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search"></i>
                        </button>
                        <?php if (!empty($filters)): ?>
                            <a href="<?= base_url('admin/customers') ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        <?php endif; ?>
                    </form>
                </div>
            </div>
        </div>

        <?php if (empty($customers)): ?>
        <div class="empty-state">
            <i class="fas fa-users"></i>
            <h5>No Customers Found</h5>
            <p class="mb-3">
                <?php if (!empty($filters['search'])): ?>
                    No customers match your search criteria.
                <?php else: ?>
                    Start by adding your first customer to the system.
                <?php endif; ?>
            </p>
            <a href="<?= base_url('admin/customers/create') ?>" class="btn btn-admin-primary">
                <i class="fas fa-plus me-2"></i>Add First Customer
            </a>
        </div>
        <?php else: ?>
        <div class="table-responsive">
            <table id="customersTable" class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Customer Code</th>
                        <th>Name</th>
                        <th>Contact</th>
                        <th>Location</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($customers as $customer): ?>
                    <tr>
                        <td>
                            <span class="fw-bold text-primary"><?= esc($customer['customer_code']) ?></span>
                        </td>
                        <td>
                            <div class="fw-bold"><?= esc($customer['first_name'] . ' ' . $customer['last_name']) ?></div>
                        </td>
                        <td>
                            <div>
                                <?php if ($customer['phone']): ?>
                                    <div><i class="fas fa-phone me-1"></i><?= esc($customer['phone']) ?></div>
                                <?php endif; ?>
                                <?php if ($customer['email']): ?>
                                    <div><i class="fas fa-envelope me-1"></i><?= esc($customer['email']) ?></div>
                                <?php endif; ?>
                                <?php if (!$customer['phone'] && !$customer['email']): ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <?php if ($customer['location_name']): ?>
                                <div class="small">
                                    <strong><?= esc($customer['location_name']) ?></strong><br>
                                    <span class="text-muted"><?= esc($customer['country_name'] . ' > ' . $customer['province_name']) ?></span>
                                </div>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="status-badge status-<?= $customer['status'] ?>">
                                <?= ucfirst(esc($customer['status'])) ?>
                            </span>
                        </td>
                        <td>
                            <small class="text-muted">
                                <?= date('M j, Y', strtotime($customer['created_at'])) ?>
                            </small>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="<?= base_url('admin/customers/' . $customer['id']) ?>" 
                                   class="btn btn-sm btn-outline-primary" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?= base_url('admin/customers/' . $customer['id'] . '/edit') ?>" 
                                   class="btn btn-sm btn-outline-success" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <?php if ($customer['status'] === 'active'): ?>
                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                            onclick="confirmStatusChange(<?= $customer['id'] ?>, 'deactivate', '<?= esc($customer['first_name'] . ' ' . $customer['last_name']) ?>')"
                                            title="Deactivate">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                <?php else: ?>
                                    <button type="button" class="btn btn-sm btn-outline-info" 
                                            onclick="confirmStatusChange(<?= $customer['id'] ?>, 'activate', '<?= esc($customer['first_name'] . ' ' . $customer['last_name']) ?>')"
                                            title="Activate">
                                        <i class="fas fa-play"></i>
                                    </button>
                                <?php endif; ?>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="confirmDelete(<?= $customer['id'] ?>, '<?= esc($customer['first_name'] . ' ' . $customer['last_name']) ?>')"
                                        title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if ($pager): ?>
        <div class="table-pagination mt-3">
            <div class="d-flex justify-content-between align-items-center">
                <div class="table-info">
                    <small class="text-muted">
                        <?php 
                        $currentPage = $pager->getCurrentPage();
                        $perPage = $pager->getPerPage();
                        $total = $pager->getTotal();
                        $start = ($currentPage - 1) * $perPage + 1;
                        $end = min($currentPage * $perPage, $total);
                        echo "Showing {$start} to {$end} of {$total} entries";
                        ?>
                    </small>
                </div>
                <div>
                    <?= $pager->links() ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Status Change Confirmation Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalTitle">Confirm Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="statusModalMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="statusForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="PATCH">
                    <button type="submit" class="btn" id="statusConfirmBtn">Confirm</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the customer <strong id="customerName"></strong>?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete Customer</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Initialize enhanced table functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedTable('customersTable', {
        searchPlaceholder: 'Search customers by name, code, phone, or email...',
        exportFilename: 'customers_report',
        rowsPerPage: 25
    });
});

// Enhanced table functionality implementation
function initializeEnhancedTable(tableId, options = {}) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const config = {
        searchPlaceholder: options.searchPlaceholder || 'Search...',
        exportFilename: options.exportFilename || 'table_export',
        rowsPerPage: options.rowsPerPage || 25,
        ...options
    };

    // Add export functionality
    addExportButton(tableId, config);
}

function addExportButton(tableId, config) {
    // Create export button in table header
    const tableHeader = document.querySelector('.table-header .row .col-md-6:first-child');
    if (tableHeader) {
        const exportBtn = document.createElement('button');
        exportBtn.className = 'btn btn-success btn-sm ms-2';
        exportBtn.innerHTML = '<i class="fas fa-file-excel me-2"></i>Export';
        exportBtn.onclick = () => exportTableToExcel(tableId, config.exportFilename);
        
        const headerContent = tableHeader.querySelector('h5');
        if (headerContent) {
            headerContent.parentNode.insertBefore(exportBtn, headerContent.nextSibling);
        }
    }
}

function exportTableToExcel(tableId, filename) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tr');
    const data = [];

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll(index === 0 ? 'th' : 'td');
        const rowData = [];

        cells.forEach((cell, cellIndex) => {
            // Skip the Actions column (last)
            if (cellIndex < cells.length - 1) {
                let cellText = cell.textContent.trim();
                rowData.push(cellText);
            }
        });

        if (rowData.length > 0) {
            data.push(rowData);
        }
    });

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(data);

    // Set column widths
    const colWidths = [
        { wch: 15 }, // Customer Code
        { wch: 25 }, // Name
        { wch: 25 }, // Contact
        { wch: 30 }, // Location
        { wch: 10 }, // Status
        { wch: 15 }  // Created
    ];
    ws['!cols'] = colWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, "Customers Report");

    // Generate filename with current date
    const exportFilename = filename + '_' + new Date().toISOString().split('T')[0] + '.xlsx';

    // Save the file
    XLSX.writeFile(wb, exportFilename);

    // Show success message
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        Excel file exported successfully!
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);
}

function confirmStatusChange(customerId, action, customerName) {
    const modal = document.getElementById('statusModal');
    const title = document.getElementById('statusModalTitle');
    const message = document.getElementById('statusModalMessage');
    const confirmBtn = document.getElementById('statusConfirmBtn');
    const form = document.getElementById('statusForm');

    if (action === 'activate') {
        title.textContent = 'Confirm Activation';
        message.textContent = `Are you sure you want to activate customer "${customerName}"?`;
        confirmBtn.textContent = 'Activate';
        confirmBtn.className = 'btn btn-info';
        form.action = '<?= base_url('admin/customers') ?>/' + customerId + '/activate';
    } else {
        title.textContent = 'Confirm Deactivation';
        message.textContent = `Are you sure you want to deactivate customer "${customerName}"?`;
        confirmBtn.textContent = 'Deactivate';
        confirmBtn.className = 'btn btn-warning';
        form.action = '<?= base_url('admin/customers') ?>/' + customerId + '/deactivate';
    }

    var statusModal = new bootstrap.Modal(modal);
    statusModal.show();
}

function confirmDelete(customerId, customerName) {
    document.getElementById('customerName').textContent = customerName;
    document.getElementById('deleteForm').action = '<?= base_url('admin/customers') ?>/' + customerId + '/delete';

    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>
<?= $this->endSection() ?>