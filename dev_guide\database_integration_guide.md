# DCBuyer Database Integration Guide

## 1. Overview

DCBuyer is a web-based commodity trading and purchasing management system built with CodeIgniter 4 and integrated with Supabase as the backend database service. This design provides a robust, scalable PostgreSQL database with modern features and real-time capabilities.

### 1.1 Purpose
- Leverage Supabase's managed PostgreSQL database for reliable data storage
- Provide secure database access with Row Level Security (RLS)
- Enable real-time data updates with Supabase subscriptions (future enhancement)
- Reduce database infrastructure complexity with managed services
- Maintain data integrity with ACID compliance and foreign key constraints

### 1.2 Supabase Services Used
- **Supabase Database** - Managed PostgreSQL database with extensions
- **Supabase Auth** - User authentication and authorization (future consideration)
- **Supabase Storage** - File and image storage (future consideration)
- **Supabase Edge Functions** - Serverless functions (future consideration)
- **Supabase Realtime** - Real-time subscriptions (future consideration)
- **Supabase Dashboard** - Database management and monitoring

## 2. CodeIgniter 4 + Supabase Architecture

### 2.1 High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │   Mobile Web    │    │   Admin Panel   │
│   (Desktop)     │    │   (Responsive)  │    │   (Bootstrap)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │ HTTP/HTTPS
         ┌─────────────────────────────────────────────────┐
         │           CodeIgniter 4 Web Application         │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │Controllers  │  │   Models    │  │  Views   ││
         │  │(Business    │  │(Data Layer) │  │(UI Layer)││
         │  │ Logic)      │  │             │  │          ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │   Routes    │  │ Libraries   │  │ Helpers  ││
         │  │(URL Mapping)│  │(Services)   │  │(Utilities)││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
                                 │ PostgreSQL Connection
         ┌─────────────────────────────────────────────────┐
         │              Supabase Services                  │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │PostgreSQL   │  │   Auth      │  │ Storage  ││
         │  │Database     │  │(Future)     │  │(Future)  ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │  Realtime   │  │Edge Functions│  │Dashboard ││
         │  │ (Future)    │  │  (Future)   │  │(Monitoring)││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
```

### 2.2 Technology Stack

**Backend Framework:**
- CodeIgniter 4 (PHP 8.1+ MVC framework)
- PHP 8.1+ (Server-side scripting language)
- Apache/Nginx (Web server)
- Composer (PHP dependency management)

**Database:**
- Supabase PostgreSQL (Managed cloud database)
- PostgreSQL 15+ (Relational database with JSONB support)
- Connection pooling and SSL encryption
- Automated backups and point-in-time recovery

**Frontend:**
- HTML5 with semantic markup and accessibility
- CSS3 with Bootstrap 5 framework
- JavaScript (ES6+) for client-side interactivity
- Responsive design for mobile compatibility

**Development Tools:**
- Supabase Dashboard for database management
- XAMPP for local development environment
- Git for version control
- VS Code with PHP extensions
- Browser DevTools for debugging

## 3. Supabase PostgreSQL Database Design

### 3.1 Database Schema Structure
```
dcbuyers_db (PostgreSQL)
├── users (implemented)
│   ├── id (bigint, primary key)
│   ├── fullname, username, email
│   ├── password_hash
│   ├── is_admin, is_buyer, is_supervisor
│   ├── reports_to (self-referencing FK)
│   ├── status (enum: active, inactive, suspended, pending)
│   └── audit fields (created_at, updated_at, etc.)
├── customers (planned)
├── commodities (planned)
├── mission (with simplified assignment fields)
├── transactions (planned)
├── file_attachments (planned)
└── audit_logs (planned)
```

### 3.2 Current Database Schema

#### Users Table (Implemented)
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    fullname TEXT NOT NULL,
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    is_admin BOOLEAN NOT NULL DEFAULT FALSE,
    is_buyer BOOLEAN NOT NULL DEFAULT FALSE,
    is_supervisor BOOLEAN NOT NULL DEFAULT FALSE,
    reports_to BIGINT REFERENCES users(id),
    status user_status NOT NULL DEFAULT 'active',
    remarks TEXT,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_by BIGINT REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_by BIGINT REFERENCES users(id),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- User status enum
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended', 'pending');
```

### 3.3 Supabase Configuration

#### Database Connection (CodeIgniter 4)
```php
// app/Config/Database.php
public array $default = [
    'DSN'          => '',
    'hostname'     => 'aws-1-ap-southeast-1.pooler.supabase.com',
    'username'     => 'postgres.ziiwdowgicworjaixldn',
    'password'     => 'Dakoii@2025',
    'database'     => 'postgres',
    'DBDriver'     => 'Postgre',
    'DBPrefix'     => '',
    'pConnect'     => false,
    'DBDebug'      => true,
    'charset'      => 'utf8',
    'port'         => 5432,
];
```

### 3.4 Future Database Tables (Planned)

#### Customers Table
```sql
CREATE TABLE customers (
    id BIGSERIAL PRIMARY KEY,
    customer_code VARCHAR(50) NOT NULL UNIQUE,
    business_name TEXT NOT NULL,
    contact_person TEXT,
    phone VARCHAR(20),
    email TEXT,
    address JSONB,
    location POINT,
    credit_limit DECIMAL(15,2),
    payment_terms TEXT,
    status customer_status NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

#### Commodities Table
```sql
CREATE TABLE commodities (
    id BIGSERIAL PRIMARY KEY,
    commodity_code VARCHAR(50) NOT NULL UNIQUE,
    name TEXT NOT NULL,
    description TEXT,
    category VARCHAR(100),
    specifications JSONB,
    base_price DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'PGK',
    unit_of_measurement VARCHAR(20),
    status commodity_status NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

#### Mission Table (with Simplified Assignment Fields)
```sql
CREATE TABLE mission (
    id BIGSERIAL PRIMARY KEY,
    mission_number VARCHAR(20) UNIQUE NOT NULL,
    mission_name VARCHAR(255) NOT NULL,
    mission_date DATE NOT NULL,
    mission_status mission_status NOT NULL DEFAULT 'pending',

    -- Simplified Assignment Fields (replaces buyer_commodity_assignments table)
    user_id BIGINT REFERENCES users(id),
    commodity_id BIGINT REFERENCES commodities(commodity_id),
    budgeted_amount DECIMAL(15,2),
    actual_amount DECIMAL(15,2) DEFAULT 0.00,

    remarks TEXT,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    created_by BIGINT REFERENCES users(id),
    updated_by BIGINT REFERENCES users(id)
);
```

**Note**: This simplified approach eliminates the need for a separate `buyer_commodity_assignments` table. Assignment information is stored directly in the mission table, creating a one-to-one relationship where each mission is assigned to exactly one buyer for exactly one commodity.

## 4. Supabase Integration Benefits

### 4.1 Current Implementation
- **PostgreSQL Database**: Robust relational database with ACID compliance
- **Managed Infrastructure**: Automatic backups, scaling, and maintenance
- **Real-time Capabilities**: Built-in real-time subscriptions (for future use)
- **Security**: Row Level Security (RLS) policies for data protection
- **Dashboard**: Web-based database management interface

### 4.2 CodeIgniter 4 Integration
```php
// Example Model using Supabase PostgreSQL
<?php
namespace App\Models;
use CodeIgniter\Model;

class TransactionModel extends Model
{
    protected $table = 'transactions';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'transaction_number', 'buyer_id', 'customer_id',
        'commodity_id', 'quantity', 'unit_price',
        'total_amount', 'payment_method', 'status'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
}
```

### 4.3 Future Enhancements with Supabase
- **Supabase Auth**: Replace CodeIgniter sessions with Supabase authentication
- **Supabase Storage**: File upload and management service
- **Edge Functions**: Serverless functions for business logic
- **Real-time Subscriptions**: Live data updates for dashboard
- **API Auto-generation**: Automatic REST API from database schema

## 5. Migration from Firebase Concepts

### 5.1 Data Structure Comparison
| Firebase Firestore | Supabase PostgreSQL |
|-------------------|-------------------|
| Collections | Tables |
| Documents | Rows |
| Subcollections | Foreign Key Relations |
| Document ID | Primary Key (BIGINT) |
| Flexible Schema | Structured Schema |
| NoSQL Queries | SQL Queries |

### 5.2 Security Model
| Firebase | Supabase |
|----------|----------|
| Security Rules | Row Level Security (RLS) |
| Client-side Auth | Server-side Sessions |
| JWT Tokens | Session Cookies |
| Role-based Rules | Database Policies |

## 6. Conclusion

This document outlines the transition from a Firebase-based PWA architecture to a CodeIgniter 4 + Supabase PostgreSQL web application. The current implementation provides:

- **Reliable Data Storage**: PostgreSQL database with ACID compliance
- **Scalable Architecture**: Managed Supabase infrastructure
- **Security**: Server-side authentication and validation
- **Performance**: Optimized SQL queries and database indexing
- **Maintainability**: Structured MVC architecture with CodeIgniter 4

The system is designed to be a traditional web application without offline-first capabilities, focusing on reliability, security, and ease of development.

---

*This document serves as a reference for the Supabase PostgreSQL integration in the DCBuyer web application built with CodeIgniter 4.*
