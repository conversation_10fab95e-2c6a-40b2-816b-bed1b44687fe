# Buy Transactions Feature - System Design & User Stories

## 📋 Feature Overview

The Buy Transactions feature is a role-specific interface for buyers to execute commodity purchases through their assigned missions. This feature integrates with the existing mission assignment system and provides a streamlined purchasing workflow.

## 🏗️ System Architecture

### Database Integration
The feature leverages the existing database schema:
- **Mission Table**: Contains buyer assignments and commodity allocations
- **Buy_Transactions Table**: Records all purchase transactions
- **Users Table**: Role validation (is_buyer = TRUE)
- **Commodities Table**: Product information and units
- **Locations Table**: Transaction location data
- **Customers Table**: Optional buyer information

### RESTful API Endpoints
```
GET    /buyer/dashboard                    # Buyer's main dashboard
GET    /buyer/missions                     # List assigned missions
GET    /buyer/missions/{id}                # Mission details & buy interface
POST   /buyer/missions/{id}/transactions   # Create buy transaction
GET    /buyer/transactions                 # Transaction history
GET    /buyer/transactions/{id}            # Transaction details
```

## 🎯 Feature Components

### 1. Mission Authorization System
**Business Logic**: 
- Only missions with `user_id` matching logged-in buyer are displayed
- Mission status must be 'pending' or 'in_progress'
- Buyers cannot access missions assigned to other users

**Database Query**:
```sql
SELECT * FROM mission 
WHERE user_id = {current_user_id} 
AND mission_status IN ('pending', 'in_progress')
AND is_deleted = FALSE
```

### 2. Buy Dashboard Interface
**Components**:
- Mission summary card (name, date, commodity, budget)
- Current spending vs budget tracker
- Large "BUY NOW" button (primary action)
- Recent transactions list for this mission
- Mission progress indicators

### 3. Transaction Creation Flow
**Validation Rules**:
- User must be assigned to mission as buyer
- Mission status must allow transactions
- Commodity must match mission assignment
- Location is required for all transactions
- Customer assignment is optional
- Amount validation: Quantity × Unit Price = Payment Amount

## 📱 User Interface Design

### Navigation Flow
```
Login → Buyer Dashboard → My Missions → Select Mission → Buy Dashboard → Create Transaction
```

### Screen Hierarchy
1. **Buyer Dashboard**: Overview of assigned missions
2. **Mission List**: Grid/list of pending and in-progress missions
3. **Mission Buy Dashboard**: Focused transaction interface
4. **Transaction Form**: Purchase details entry
5. **Transaction Confirmation**: Success/error feedback

## 📝 User Stories

### Epic: Buy Transactions Management

#### Primary User Stories

**US-001: Mission Access Control**
- **As a buyer**, I want to see only missions assigned to me, so that I focus on my specific responsibilities
- **Given** I am logged in as a buyer
- **When** I navigate to the missions page
- **Then** I should only see missions where I am the assigned buyer
- **And** missions should be filtered to 'pending' and 'in_progress' status only

**US-002: Mission Dashboard Navigation**
- **As a buyer**, I want to navigate from Dashboard → Missions → Buy Interface, so that I can quickly access my purchasing tasks
- **Given** I am on the buyer dashboard
- **When** I click on "My Missions"
- **Then** I should see a list of missions assigned to me
- **And** each mission should show key details (name, commodity, budget, status)

**US-003: Mission Selection and Buy Dashboard**
- **As a buyer**, I want to click on a mission and see a dedicated buy dashboard, so that I can focus on purchasing for that specific mission
- **Given** I am viewing my missions list
- **When** I click on a specific mission
- **Then** I should see the mission buy dashboard
- **And** the dashboard should display mission details, budget tracking, and a prominent "BUY NOW" button

**US-004: Transaction Creation**
- **As a buyer**, I want to click the "BUY NOW" button to create a transaction, so that I can record my commodity purchases
- **Given** I am on the mission buy dashboard
- **When** I click the "BUY NOW" button
- **Then** I should see a transaction creation form
- **And** the form should be pre-populated with mission commodity and my user information

**US-005: Transaction Form Completion**
- **As a buyer**, I want to enter transaction details (quantity, location, customer), so that I can record complete purchase information
- **Given** I am on the transaction creation form
- **When** I enter quantity, select location, and optionally select/add customer
- **Then** the system should calculate total amount automatically
- **And** I should be able to submit the transaction

**US-006: Budget Tracking**
- **As a buyer**, I want to see budget vs actual spending on the mission dashboard, so that I can manage my purchasing within budget limits
- **Given** I am viewing a mission buy dashboard
- **When** I look at the budget section
- **Then** I should see budgeted amount, spent amount, and remaining budget
- **And** the system should warn me if I'm approaching or exceeding budget

#### Secondary User Stories

**US-007: Transaction History**
- **As a buyer**, I want to view my transaction history for each mission, so that I can review my purchasing activity
- **Given** I am on a mission buy dashboard
- **When** I view the recent transactions section
- **Then** I should see my recent transactions for this mission
- **And** each transaction should show date, quantity, amount, and customer

**US-008: Customer Management**
- **As a buyer**, I want to select existing customers or add new customers during transaction creation, so that I can maintain customer relationships
- **Given** I am creating a transaction
- **When** I reach the customer field
- **Then** I should be able to select from existing customers or add a new customer
- **And** customer selection should be optional

**US-009: Location Selection**
- **As a buyer**, I want to select a location for each transaction, so that purchases can be geographically tracked
- **Given** I am creating a transaction
- **When** I reach the location field
- **Then** I should be able to select from the location hierarchy (Country → Province → District → LLG → Location)
- **And** location selection should be mandatory

**US-010: Transaction Confirmation**
- **As a buyer**, I want to receive confirmation after creating a transaction, so that I know my purchase was recorded successfully
- **Given** I have submitted a transaction form
- **When** the transaction is processed
- **Then** I should see a success confirmation message
- **And** I should be redirected back to the mission buy dashboard with updated budget information

#### Administrative User Stories

**US-011: Mission Assignment Enforcement**
- **As the system**, I want to prevent buyers from accessing missions not assigned to them, so that data security and role separation is maintained
- **Given** a buyer attempts to access a mission
- **When** the system checks mission assignments
- **Then** access should only be granted if the buyer is assigned to that mission
- **And** unauthorized access attempts should be logged

**US-012: Mission Status Validation**
- **As the system**, I want to only allow transactions on active missions, so that purchases are made within valid time frames
- **Given** a buyer attempts to create a transaction
- **When** the system validates mission status
- **Then** transactions should only be allowed for 'pending' and 'in_progress' missions
- **And** completed or cancelled missions should not allow new transactions

## 🔧 Technical Implementation

### Controller Structure
```php
class BuyerController extends BaseController
{
    public function dashboard()        // Buyer main dashboard
    public function missions()         // List assigned missions  
    public function missionBuy($id)    // Mission buy dashboard
    public function createTransaction($missionId)  // Transaction form
    public function storeTransaction($missionId)   // Process transaction
    public function transactionHistory()          // View transaction history
}
```

### Validation Rules
```php
// Transaction Creation Validation
'mission_id' => 'required|integer|is_assigned_to_user',
'commodity_id' => 'required|integer|matches_mission_commodity',
'quantity' => 'required|decimal|greater_than[0]',
'location_id' => 'required|integer|exists[locations.id]',
'customer_id' => 'permit_empty|integer|exists[customers.id]',
'payment_amount' => 'required|decimal|greater_than[0]'
```

### Security Measures
- **Mission Authorization**: Verify buyer assignment before allowing access
- **Transaction Validation**: Ensure commodity matches mission assignment
- **Input Sanitization**: Validate all form inputs server-side
- **CSRF Protection**: Include CSRF tokens on all forms
- **Audit Logging**: Track all transaction creation and mission access

## 📊 Success Metrics

### Business Metrics
- **Transaction Volume**: Number of transactions created per mission
- **Budget Adherence**: Percentage of missions staying within budget
- **Mission Completion**: Rate of missions progressing from pending to completed
- **User Engagement**: Frequency of buyer logins and mission access

### Technical Metrics
- **Response Time**: Mission dashboard load time < 2 seconds
- **Error Rate**: Transaction creation success rate > 95%
- **Data Integrity**: Zero unauthorized mission access attempts
- **Performance**: Support concurrent buyer transactions

## 🚀 Implementation Priority

### Phase 1: Core Functionality (Week 1-2)
1. Mission authorization and filtering
2. Basic buy dashboard interface
3. Transaction creation form
4. Budget tracking display

### Phase 2: Enhanced Features (Week 3)
1. Transaction history display
2. Customer selection/creation
3. Advanced validation rules
4. Success/error messaging

### Phase 3: Optimization (Week 4)
1. Performance optimization
2. Mobile responsive design
3. Advanced security measures
4. Comprehensive testing

---

*This design integrates seamlessly with the existing DCBuyer CodeIgniter 4 architecture and Supabase PostgreSQL database, providing buyers with a streamlined, secure, and efficient transaction creation workflow.*