-- Define the mission_status ENUM type
CREATE TYPE mission_status AS ENUM ('pending', 'in_progress', 'completed', 'cancelled');

-- Create the mission table
CREATE TABLE mission (
    -- Primary key
    id SERIAL PRIMARY KEY,
    
    -- Core fields
    mission_name VARCHAR(255) NOT NULL,
    mission_date DATE NOT NULL,
    mission_status mission_status NOT NULL DEFAULT 'pending',
    remarks TEXT NULL,
    
    -- Audit fields
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INT,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by INT,
    
    -- Soft delete fields
    deleted_at TIMESTAMP NULL,
    deleted_by INT NULL,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE
);

-- Create indexes for better performance
CREATE INDEX idx_mission_name ON mission(mission_name);
CREATE INDEX idx_mission_date ON mission(mission_date);
CREATE INDEX idx_mission_status ON mission(mission_status);
CREATE INDEX idx_created_at ON mission(created_at);
CREATE INDEX idx_deleted_at ON mission(deleted_at);
CREATE INDEX idx_is_deleted ON mission(is_deleted);

-- Add a comment to describe the table
COMMENT ON TABLE mission IS 'Stores mission information with status tracking and audit fields';