<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<!-- SheetJS for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<style>
    /* Custom Table Controls Styling */
    .table-controls {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-controls input[type="text"] {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 0.5rem;
    }

    .table-controls input[type="text"]:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .table-pagination {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-info {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .pagination .page-link {
        border-radius: 8px;
        margin: 0 2px;
        border: 1px solid #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
    }

    .mission-card {
        transition: transform 0.2s ease-in-out;
    }

    .mission-card:hover {
        transform: translateY(-2px);
    }

    .budget-info {
        font-size: 0.9rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= number_format($stats['total_missions']) ?></h4>
                        <p class="mb-0">Total Missions</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-bullseye fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= number_format($stats['in_progress_missions']) ?></h4>
                        <p class="mb-0">In Progress</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= number_format($stats['completed_missions']) ?></h4>
                        <p class="mb-0">Completed</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= number_format($transaction_stats['total_transactions']) ?></h4>
                        <p class="mb-0">My Transactions</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-receipt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Missions Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">My Assigned Missions</h5>
    </div>
    <div class="card-body">
        <?php if (empty($missions)): ?>
            <div class="text-center py-4">
                <i class="fas fa-bullseye fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No missions assigned</h5>
                <p class="text-muted">You don't have any missions assigned to you yet. Contact your administrator for mission assignments.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table id="missionsTable" class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>Mission Number</th>
                            <th>Mission Name</th>
                            <th>Commodity</th>
                            <th>Location</th>
                            <th>Budget</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($missions as $mission): ?>
                            <tr>
                                <td>
                                    <strong><?= esc($mission['mission_number']) ?></strong>
                                </td>
                                <td>
                                    <div class="fw-bold"><?= esc($mission['mission_name']) ?></div>
                                    <?php if (!empty($mission['mission_description'])): ?>
                                        <small class="text-muted"><?= esc(substr($mission['mission_description'], 0, 50)) ?>...</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div><?= esc($mission['commodity_name']) ?></div>
                                    <small class="text-muted"><?= esc($mission['unit_of_measurement']) ?></small>
                                </td>
                                <td>
                                    <div><?= esc($mission['location_name']) ?></div>
                                    <small class="text-muted"><?= esc($mission['district_name']) ?>, <?= esc($mission['province_name']) ?></small>
                                </td>
                                <td>
                                    <div class="budget-info">
                                        <div><strong>PGK <?= number_format($mission['budgeted_amount'], 2) ?></strong></div>
                                        <small class="text-muted">Spent: PGK <?= number_format($mission['actual_amount'], 2) ?></small>
                                    </div>
                                </td>
                                <td>
                                    <?= ucfirst(str_replace('_', ' ', $mission['mission_status'])) ?>
                                </td>
                                <td>
                                    <a href="<?= base_url('admin/transactions?mission_id=' . $mission['id']) ?>" 
                                       class="btn btn-primary btn-sm" title="View Transactions">
                                        <i class="fas fa-receipt me-1"></i>Transactions
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Enhanced Table Implementation
function initializeEnhancedTable(tableId, options = {}) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const config = {
        searchPlaceholder: options.searchPlaceholder || 'Search...',
        exportFilename: options.exportFilename || 'table_export',
        rowsPerPage: options.rowsPerPage || 25,
        ...options
    };

    // Add search functionality
    addTableSearch(tableId, config);

    // Add export functionality
    addExportButton(tableId, config);

    // Add pagination
    addTablePagination(tableId, config);

    // Initialize existing admin template table features
    if (typeof AdminTemplate !== 'undefined' && AdminTemplate.initializeDataTable) {
        AdminTemplate.initializeDataTable(tableId, {
            sortable: true,
            searchable: true
        });
    }
}

function addTableSearch(tableId, config) {
    const table = document.getElementById(tableId);
    const tableContainer = table.closest('.table-responsive').parentNode;

    // Create search container
    const searchContainer = document.createElement('div');
    searchContainer.className = 'table-controls mb-3 d-flex justify-content-between align-items-center';

    // Create search input
    const searchDiv = document.createElement('div');
    searchDiv.className = 'd-flex align-items-center';
    searchDiv.innerHTML = `
        <label class="me-2 mb-0">Search:</label>
        <input type="text" id="${tableId}Search" class="form-control" style="width: 300px;" placeholder="${config.searchPlaceholder}">
    `;

    // Create export button
    const exportDiv = document.createElement('div');
    exportDiv.innerHTML = `
        <button type="button" id="${tableId}ExportExcel" class="btn btn-success">
            <i class="fas fa-file-excel me-2"></i>Export to Excel
        </button>
    `;

    searchContainer.appendChild(searchDiv);
    searchContainer.appendChild(exportDiv);
    tableContainer.insertBefore(searchContainer, tableContainer.firstChild);

    // Add search functionality
    const searchInput = document.getElementById(`${tableId}Search`);
    searchInput.addEventListener('input', function() {
        filterTable(tableId, this.value.toLowerCase());
    });
}

function filterTable(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    updateTableInfo(tableId);
}

function addExportButton(tableId, config) {
    const exportBtn = document.getElementById(`${tableId}ExportExcel`);
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportTableToExcel(tableId, config.exportFilename);
        });
    }
}

function exportTableToExcel(tableId, filename) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tr');
    const data = [];

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll(index === 0 ? 'th' : 'td');
        const rowData = [];

        cells.forEach((cell, cellIndex) => {
            // Skip the Actions column (last column)
            if (cellIndex < cells.length - 1) {
                let cellText = cell.textContent.trim();
                // Clean up status badges
                if (cell.querySelector('.badge')) {
                    cellText = cell.querySelector('.badge').textContent.trim();
                }
                // Clean up currency formatting for numbers
                if (cellText.startsWith('PGK')) {
                    const numValue = parseFloat(cellText.replace(/[PGK,]/g, ''));
                    rowData.push(isNaN(numValue) ? cellText : numValue);
                } else {
                    rowData.push(cellText);
                }
            }
        });

        if (rowData.length > 0) {
            data.push(rowData);
        }
    });

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(data);

    // Set column widths
    const colWidths = data[0].map(() => ({ wch: 15 }));
    ws['!cols'] = colWidths;

    // Style the header row
    const headerRange = XLSX.utils.decode_range(ws['!ref']);
    for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!ws[cellAddress]) continue;
        ws[cellAddress].s = {
            font: { bold: true, color: { rgb: "FFFFFF" } },
            fill: { fgColor: { rgb: "2E7D32" } },
            alignment: { horizontal: "center" }
        };
    }

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, "My Missions");

    // Generate filename with current date
    const exportFilename = filename + '_' + new Date().toISOString().split('T')[0] + '.xlsx';

    // Save the file
    XLSX.writeFile(wb, exportFilename);

    if (typeof AdminTemplate !== 'undefined') {
        AdminTemplate.showNotification('Excel file exported successfully!', 'success');
    }
}

function addTablePagination(tableId, config) {
    const table = document.getElementById(tableId);
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    if (rows.length <= config.rowsPerPage) return; // No pagination needed for small tables

    let currentPage = 1;
    const rowsPerPage = config.rowsPerPage;
    const totalPages = Math.ceil(rows.length / rowsPerPage);

    // Create pagination container
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'table-pagination mt-3 d-flex justify-content-between align-items-center';
    paginationContainer.innerHTML = `
        <div class="table-info">
            <span id="${tableId}TableInfo">Showing 1 to ${Math.min(rowsPerPage, rows.length)} of ${rows.length} entries</span>
        </div>
        <nav>
            <ul class="pagination mb-0" id="${tableId}TablePagination">
                <!-- Pagination buttons will be generated here -->
            </ul>
        </nav>
    `;

    table.parentNode.appendChild(paginationContainer);

    function showPage(page) {
        const start = (page - 1) * rowsPerPage;
        const end = start + rowsPerPage;

        rows.forEach((row, index) => {
            if (index >= start && index < end && row.style.display !== 'none') {
                row.style.display = '';
            } else if (row.style.display !== 'none') {
                row.style.display = 'none';
            }
        });

        updatePagination(tableId, page, totalPages, showPage);
        updateTableInfo(tableId);
    }

    // Initialize first page
    showPage(1);
}

function updatePagination(tableId, currentPage, totalPages, showPageCallback) {
    const pagination = document.getElementById(`${tableId}TablePagination`);
    pagination.innerHTML = '';

    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>`;
    pagination.appendChild(prevLi);

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
        pagination.appendChild(li);
    }

    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>`;
    pagination.appendChild(nextLi);

    // Add click events
    pagination.addEventListener('click', function(e) {
        e.preventDefault();
        if (e.target.classList.contains('page-link') && !e.target.parentNode.classList.contains('disabled')) {
            const page = parseInt(e.target.dataset.page);
            if (page >= 1 && page <= totalPages) {
                showPageCallback(page);
            }
        }
    });
}

function updateTableInfo(tableId) {
    const tableInfo = document.getElementById(`${tableId}TableInfo`);
    if (!tableInfo) return;

    const table = document.getElementById(tableId);
    const visibleRows = table.querySelectorAll('tbody tr[style=""], tbody tr:not([style])');
    const totalRows = table.querySelectorAll('tbody tr').length;

    tableInfo.textContent = `Showing ${visibleRows.length} of ${totalRows} entries`;
}

// Initialize the enhanced table when document is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedTable('missionsTable', {
        searchPlaceholder: 'Search missions by number, name, commodity, location...',
        exportFilename: 'my_missions',
        rowsPerPage: 25
    });
});
</script>
<?= $this->endSection() ?>
