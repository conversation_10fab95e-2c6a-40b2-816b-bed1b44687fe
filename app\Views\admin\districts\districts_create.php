<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .form-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .form-section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #2E7D32;
        box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
    }
    
    .required {
        color: #dc3545;
    }
    
    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .info-box {
        background-color: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .info-box h6 {
        color: #1976d2;
        margin-bottom: 0.5rem;
    }
    
    .info-box p {
        margin-bottom: 0;
        font-size: 0.9rem;
        color: #424242;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Display Validation Errors -->
    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please correct the following errors:</h6>
            <ul class="mb-0">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Display Error Message -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <form action="<?= base_url('admin/districts') ?>" method="POST" id="districtForm">
        <?= csrf_field() ?>
        
        <!-- Information Box -->
        <div class="info-box">
            <h6><i class="fas fa-info-circle me-2"></i>District Information</h6>
            <p>Districts are the third level of the location hierarchy under provinces. Once created, you can add LLGs and specific locations under this district.</p>
        </div>
        
        <!-- District Details Section -->
        <div class="form-section">
            <h5 class="form-section-title">
                <i class="fas fa-map-marked-alt me-2"></i>District Details
            </h5>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="country_id" class="form-label">
                            Country <span class="required">*</span>
                        </label>
                        <select class="form-select" id="country_id" name="country_id" required>
                            <option value="">Select a country</option>
                            <?php foreach ($countries as $country): ?>
                                <option value="<?= $country['id'] ?>" <?= old('country_id') == $country['id'] ? 'selected' : '' ?>>
                                    <?= esc($country['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Select the country first to load provinces.</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="province_id" class="form-label">
                            Province <span class="required">*</span>
                        </label>
                        <select class="form-select" id="province_id" name="province_id" required disabled>
                            <option value="">Select a province</option>
                        </select>
                        <div class="form-text">Select the province this district belongs to.</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group">
                        <label for="name" class="form-label">
                            District Name <span class="required">*</span>
                        </label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="<?= old('name') ?>" required maxlength="100" 
                               placeholder="Enter district name">
                        <div class="form-text">Full name of the district (max 100 characters).</div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="code" class="form-label">District Code</label>
                        <input type="text" class="form-control" id="code" name="code" 
                               value="<?= old('code') ?>" maxlength="10" placeholder="e.g., MSD, NFD">
                        <div class="form-text">Optional short code (max 10 characters).</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="form-section">
            <div class="d-flex justify-content-between">
                <a href="<?= base_url('admin/districts') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Cancel
                </a>
                <button type="submit" class="btn btn-admin-primary">
                    <i class="fas fa-save me-2"></i>Create District
                </button>
            </div>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const countrySelect = document.getElementById('country_id');
    const provinceSelect = document.getElementById('province_id');
    
    // Handle country change to load provinces
    countrySelect.addEventListener('change', function() {
        const countryId = this.value;
        
        // Reset province dropdown
        provinceSelect.innerHTML = '<option value="">Select a province</option>';
        provinceSelect.disabled = !countryId;
        
        if (countryId) {
            // Load provinces for selected country
            fetch(`<?= base_url('admin/locations/api/provinces') ?>/${countryId}`, {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        data.data.forEach(province => {
                            const option = document.createElement('option');
                            option.value = province.id;
                            option.textContent = province.name;
                            provinceSelect.appendChild(option);
                        });
                    } else {
                        console.error('Error loading provinces:', data.message);
                        alert('Error loading provinces: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error loading provinces:', error);
                    alert('Error loading provinces. Please try again.');
                });
        }
    });
    
    // Form validation
    const form = document.getElementById('districtForm');
    
    form.addEventListener('submit', function(e) {
        const provinceId = document.getElementById('province_id').value;
        const name = document.getElementById('name').value.trim();
        
        if (!provinceId) {
            e.preventDefault();
            alert('Please select a province.');
            document.getElementById('province_id').focus();
            return false;
        }
        
        if (!name) {
            e.preventDefault();
            alert('Please enter a district name.');
            document.getElementById('name').focus();
            return false;
        }
    });
});
</script>
<?= $this->endSection() ?>
