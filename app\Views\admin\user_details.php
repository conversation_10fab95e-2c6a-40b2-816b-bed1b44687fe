<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .user-details-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .user-avatar-large {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid #f8f9fa;
    }
    
    .user-header {
        display: flex;
        align-items: center;
        gap: 2rem;
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 2px solid #f1f3f4;
    }
    
    .user-info h2 {
        margin: 0 0 0.5rem 0;
        color: #495057;
    }
    
    .user-info .user-email {
        color: #6c757d;
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }
    
    .user-roles {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    
    .role-badge {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
    }
    
    .status-badge {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
    }
    
    .detail-section {
        margin-bottom: 2rem;
    }
    
    .detail-section h4 {
        color: #495057;
        margin-bottom: 1rem;
        font-weight: 600;
        border-bottom: 1px solid #f1f3f4;
        padding-bottom: 0.5rem;
    }
    
    .detail-row {
        display: flex;
        margin-bottom: 1rem;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .detail-row:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        font-weight: 600;
        color: #495057;
        width: 200px;
        flex-shrink: 0;
    }
    
    .detail-value {
        color: #6c757d;
        flex: 1;
    }
    
    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
    }
    
    .btn-admin-primary {
        background: #2E7D32;
        border-color: #2E7D32;
        color: white;
        padding: 0.75rem 2rem;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .btn-admin-primary:hover {
        background: #1B5E20;
        border-color: #1B5E20;
        color: white;
    }
    
    .activity-timeline {
        position: relative;
        padding-left: 2rem;
    }
    
    .activity-timeline::before {
        content: '';
        position: absolute;
        left: 0.5rem;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .activity-item {
        position: relative;
        margin-bottom: 1.5rem;
        padding-left: 1.5rem;
    }
    
    .activity-item::before {
        content: '';
        position: absolute;
        left: -0.5rem;
        top: 0.25rem;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #2E7D32;
        border: 2px solid white;
        box-shadow: 0 0 0 2px #dee2e6;
    }
    
    .activity-time {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }
    
    .activity-description {
        color: #495057;
        font-weight: 500;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- User Header -->
<div class="user-details-card">
    <div class="user-header">
        <img src="<?= base_url('public/assets/images/default-avatar.svg') ?>" 
             alt="<?= esc($user['fullname']) ?>" class="user-avatar-large">
        <div class="user-info">
            <h2><?= esc($user['fullname']) ?></h2>
            <div class="user-email"><?= esc($user['email']) ?></div>
            <div class="user-roles">
                <?php if (isset($user['is_admin']) && $user['is_admin'] === true): ?>
                    <span class="badge bg-danger role-badge">Administrator</span>
                <?php endif; ?>
                <?php if (isset($user['is_supervisor']) && $user['is_supervisor'] === true): ?>
                    <span class="badge bg-warning role-badge">Supervisor</span>
                <?php endif; ?>
                <?php if (isset($user['is_buyer']) && $user['is_buyer'] === true): ?>
                    <span class="badge bg-info role-badge">Buyer</span>
                <?php endif; ?>
                <?php
                $statusClass = match($user['status']) {
                    'active' => 'bg-success',
                    'inactive' => 'bg-secondary',
                    'suspended' => 'bg-danger',
                    'pending' => 'bg-warning',
                    default => 'bg-secondary'
                };
                ?>
                <span class="badge <?= $statusClass ?> status-badge"><?= ucfirst(esc($user['status'])) ?></span>
            </div>
        </div>
    </div>
    
    <div class="action-buttons">
        <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Users
        </a>
        <a href="<?= base_url('admin/users/edit/' . $user['id']) ?>" class="btn btn-admin-primary">
            <i class="fas fa-edit me-2"></i>Edit User
        </a>
    </div>
</div>

<!-- User Details -->
<div class="row">
    <div class="col-md-8">
        <div class="user-details-card">
            <div class="detail-section">
                <h4><i class="fas fa-user me-2"></i>Basic Information</h4>
                
                <div class="detail-row">
                    <div class="detail-label">Full Name:</div>
                    <div class="detail-value"><?= esc($user['fullname']) ?></div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">Username:</div>
                    <div class="detail-value"><?= esc($user['username']) ?></div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">Email Address:</div>
                    <div class="detail-value"><?= esc($user['email']) ?></div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">Account Status:</div>
                    <div class="detail-value">
                        <span class="badge <?= $statusClass ?>"><?= ucfirst(esc($user['status'])) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h4><i class="fas fa-shield-alt me-2"></i>Roles & Permissions</h4>
                
                <div class="detail-row">
                    <div class="detail-label">Administrator:</div>
                    <div class="detail-value">
                        <?php if ($user['is_admin']): ?>
                            <i class="fas fa-check-circle text-success me-1"></i>Yes
                        <?php else: ?>
                            <i class="fas fa-times-circle text-danger me-1"></i>No
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">Supervisor:</div>
                    <div class="detail-value">
                        <?php if ($user['is_supervisor']): ?>
                            <i class="fas fa-check-circle text-success me-1"></i>Yes
                        <?php else: ?>
                            <i class="fas fa-times-circle text-danger me-1"></i>No
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">Buyer:</div>
                    <div class="detail-value">
                        <?php if ($user['is_buyer']): ?>
                            <i class="fas fa-check-circle text-success me-1"></i>Yes
                        <?php else: ?>
                            <i class="fas fa-times-circle text-danger me-1"></i>No
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h4><i class="fas fa-calendar me-2"></i>Account Information</h4>
                
                <div class="detail-row">
                    <div class="detail-label">Member Since:</div>
                    <div class="detail-value"><?= date('F j, Y', strtotime($user['created_at'])) ?></div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">Last Updated:</div>
                    <div class="detail-value"><?= date('F j, Y g:i A', strtotime($user['updated_at'])) ?></div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">Last Login:</div>
                    <div class="detail-value">
                        <span class="text-muted">Never logged in</span>
                    </div>
                </div>
            </div>
            
            <?php if (!empty($user['remarks'])): ?>
            <div class="detail-section">
                <h4><i class="fas fa-sticky-note me-2"></i>Remarks</h4>
                <div class="detail-row">
                    <div class="detail-value"><?= nl2br(esc($user['remarks'])) ?></div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="user-details-card">
            <h4><i class="fas fa-chart-line me-2"></i>Activity Summary</h4>
            
            <div class="detail-row">
                <div class="detail-label">Total Orders:</div>
                <div class="detail-value">0</div>
            </div>
            
            <div class="detail-row">
                <div class="detail-label">Total Spent:</div>
                <div class="detail-value">₱0.00</div>
            </div>
            
            <div class="detail-row">
                <div class="detail-label">Products Listed:</div>
                <div class="detail-value">0</div>
            </div>
        </div>
        
        <div class="user-details-card">
            <h4><i class="fas fa-history me-2"></i>Recent Activity</h4>
            
            <div class="activity-timeline">
                <div class="activity-item">
                    <div class="activity-time"><?= date('M j, Y g:i A', strtotime($user['created_at'])) ?></div>
                    <div class="activity-description">Account created</div>
                </div>
                
                <?php if ($user['updated_at'] !== $user['created_at']): ?>
                <div class="activity-item">
                    <div class="activity-time"><?= date('M j, Y g:i A', strtotime($user['updated_at'])) ?></div>
                    <div class="activity-description">Profile updated</div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any specific JavaScript for user details page here
    console.log('User details page loaded');
});
</script>
<?= $this->endSection() ?>
