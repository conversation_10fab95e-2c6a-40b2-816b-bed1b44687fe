<?php

namespace App\Controllers;

use App\Models\LocationModel;
use App\Models\CountryModel;
use App\Models\ProvinceModel;
use App\Models\DistrictModel;
use App\Models\LlgModel;

class LocationsController extends BaseController
{
    protected $locationModel;
    protected $countryModel;
    protected $provinceModel;
    protected $districtModel;
    protected $llgModel;
    protected $data = [];

    public function __construct()
    {
        $this->locationModel = new LocationModel();
        $this->countryModel = new CountryModel();
        $this->provinceModel = new ProvinceModel();
        $this->districtModel = new DistrictModel();
        $this->llgModel = new LlgModel();
    }

    /**
     * Display list of locations (GET)
     */
    public function index()
    {
        $search = $this->request->getGet('search') ?? '';
        $perPage = 20;
        
        // Get filter parameters
        $filters = [
            'country_id' => $this->request->getGet('country_id'),
            'province_id' => $this->request->getGet('province_id'),
            'district_id' => $this->request->getGet('district_id'),
            'llg_id' => $this->request->getGet('llg_id')
        ];
        
        // Get locations with pagination and search
        $locations = $this->locationModel->getLocationsWithHierarchy($perPage, $search, $filters);
        $pager = $this->locationModel->pager;
        
        // Get location statistics
        $stats = $this->locationModel->getLocationStats();
        
        // Get filter options
        $countries = $this->countryModel->getForDropdown();

        $this->data['title'] = 'Locations Management - DCBuyer Admin';
        $this->data['active_menu'] = 'locations';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Locations', 'url' => base_url('admin/locations')]
        ];
        
        $this->data['page_title'] = 'Locations Management';
        $this->data['page_description'] = 'Manage geographic locations with hierarchical structure.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/locations/create') . '" class="btn btn-admin-primary">
                <i class="fas fa-map-marker-plus me-2"></i>Add New Location
            </a>
        ';
        
        $this->data['locations'] = $locations;
        $this->data['pager'] = $pager;
        $this->data['stats'] = $stats;
        $this->data['search'] = $search;
        $this->data['filters'] = $filters;
        $this->data['countries'] = $countries;
        
        return view('admin/locations/locations_index', $this->data);
    }

    /**
     * Show create location form (GET)
     */
    public function create()
    {
        $this->data['title'] = 'Create Location - DCBuyer Admin';
        $this->data['active_menu'] = 'locations';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Locations', 'url' => base_url('admin/locations')],
            ['title' => 'Create Location', 'url' => base_url('admin/locations/create')]
        ];

        $this->data['page_title'] = 'Create New Location';
        $this->data['page_description'] = 'Add a new location to the system.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/locations') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Locations
            </a>
        ';

        // Get countries for dropdown
        $this->data['countries'] = $this->countryModel->getForDropdown();

        return view('admin/locations/locations_create', $this->data);
    }

    /**
     * Store new location (POST)
     */
    public function store()
    {
        $rules = [
            'country_id' => 'required|integer',
            'province_id' => 'required|integer',
            'district_id' => 'required|integer',
            'llg_id' => 'required|integer',
            'ward' => 'required|min_length[2]|max_length[100]',
            'location_name' => 'required|min_length[2]|max_length[100]',
            'gps_latitude' => 'permit_empty|decimal',
            'gps_longitude' => 'permit_empty|decimal',
            'remarks' => 'permit_empty|max_length[1000]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'district_id' => $this->request->getPost('district_id'),
            'llg_id' => $this->request->getPost('llg_id'),
            'ward' => $this->request->getPost('ward'),
            'location_name' => $this->request->getPost('location_name'),
            'gps_latitude' => $this->request->getPost('gps_latitude') ?: null,
            'gps_longitude' => $this->request->getPost('gps_longitude') ?: null,
            'remarks' => $this->request->getPost('remarks')
        ];
        
        try {
            $result = $this->locationModel->createLocation($data, session()->get('user_id'));
            
            if ($result) {
                return redirect()->to('admin/locations')->with('success', 'Location created successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to create location.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating location: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while creating the location.');
        }
    }

    /**
     * View location details (GET)
     */
    public function show($id)
    {
        $location = $this->locationModel->getLocationWithHierarchy($id);

        if (!$location) {
            return redirect()->to('admin/locations')->with('error', 'Location not found.');
        }

        $this->data['location'] = $location;
        $this->data['title'] = 'Location Details - DCBuyer Admin';
        $this->data['active_menu'] = 'locations';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Locations', 'url' => base_url('admin/locations')],
            ['title' => 'Location Details', 'url' => base_url('admin/locations/' . $id)]
        ];
        
        $this->data['page_title'] = 'Location Details: ' . $location['location_name'];
        $this->data['page_description'] = 'View detailed information about this location.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/locations/' . $id . '/edit') . '" class="btn btn-admin-primary">
                <i class="fas fa-edit me-2"></i>Edit Location
            </a>
            <a href="' . base_url('admin/locations') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Locations
            </a>
        ';
        
        return view('admin/locations/locations_show', $this->data);
    }

    /**
     * Show edit location form (GET)
     */
    public function edit($id)
    {
        $location = $this->locationModel->getLocationWithHierarchy($id);

        if (!$location) {
            return redirect()->to('admin/locations')->with('error', 'Location not found.');
        }

        $this->data['location'] = $location;
        $this->data['title'] = 'Edit Location - DCBuyer Admin';
        $this->data['active_menu'] = 'locations';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Locations', 'url' => base_url('admin/locations')],
            ['title' => 'Edit Location', 'url' => base_url('admin/locations/' . $id . '/edit')]
        ];
        
        $this->data['page_title'] = 'Edit Location: ' . $location['location_name'];
        $this->data['page_description'] = 'Update location information.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/locations/' . $id) . '" class="btn btn-secondary">
                <i class="fas fa-eye me-2"></i>View Details
            </a>
            <a href="' . base_url('admin/locations') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Locations
            </a>
        ';

        // Get dropdown data
        $this->data['countries'] = $this->countryModel->getForDropdown();
        $this->data['provinces'] = $this->provinceModel->getByCountryForDropdown($location['country_id']);
        $this->data['districts'] = $this->districtModel->getByProvinceForDropdown($location['province_id']);
        $this->data['llgs'] = $this->llgModel->getByDistrictForDropdown($location['district_id']);

        return view('admin/locations/locations_edit', $this->data);
    }

    /**
     * Update location (PUT/PATCH)
     */
    public function update($id)
    {
        $location = $this->locationModel->find($id);

        if (!$location) {
            return redirect()->to('admin/locations')->with('error', 'Location not found.');
        }
        
        $rules = [
            'country_id' => 'required|integer',
            'province_id' => 'required|integer',
            'district_id' => 'required|integer',
            'llg_id' => 'required|integer',
            'ward' => 'required|min_length[2]|max_length[100]',
            'location_name' => 'required|min_length[2]|max_length[100]',
            'gps_latitude' => 'permit_empty|decimal',
            'gps_longitude' => 'permit_empty|decimal',
            'remarks' => 'permit_empty|max_length[1000]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'country_id' => $this->request->getPost('country_id'),
            'province_id' => $this->request->getPost('province_id'),
            'district_id' => $this->request->getPost('district_id'),
            'llg_id' => $this->request->getPost('llg_id'),
            'ward' => $this->request->getPost('ward'),
            'location_name' => $this->request->getPost('location_name'),
            'gps_latitude' => $this->request->getPost('gps_latitude') ?: null,
            'gps_longitude' => $this->request->getPost('gps_longitude') ?: null,
            'remarks' => $this->request->getPost('remarks')
        ];
        
        try {
            $result = $this->locationModel->updateLocation($id, $data, session()->get('user_id'));
            
            if ($result) {
                return redirect()->to('admin/locations/' . $id)->with('success', 'Location updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update location.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error updating location: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating the location.');
        }
    }

    /**
     * Delete location (DELETE)
     */
    public function delete($id)
    {
        $location = $this->locationModel->find($id);

        if (!$location) {
            return redirect()->to('admin/locations')->with('error', 'Location not found.');
        }

        try {
            $result = $this->locationModel->delete($id);

            if ($result) {
                return redirect()->to('admin/locations')->with('success', 'Location deleted successfully.');
            } else {
                return redirect()->to('admin/locations')->with('error', 'Failed to delete location.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error deleting location: ' . $e->getMessage());
            return redirect()->to('admin/locations')->with('error', 'An error occurred while deleting the location.');
        }
    }

    // ========================================
    // API Methods for Cascading Dropdowns
    // ========================================

    /**
     * Get provinces by country (API)
     */
    public function getProvinces($countryId)
    {
        // Temporarily disable error display to prevent HTML contamination
        $old_display_errors = ini_get('display_errors');
        ini_set('display_errors', '0');
        
        // Clean any output buffers to prevent HTML contamination
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        // Start clean output buffering
        ob_start();
        
        // Set headers directly
        header('Content-Type: application/json');
        header('Cache-Control: no-cache, must-revalidate');

        try {
            // Validate country ID
            if (!$countryId || !is_numeric($countryId)) {
                $response = [
                    'success' => false,
                    'message' => 'Invalid country ID provided'
                ];
                
                ob_clean();
                echo json_encode($response);
                
                // Restore error display setting
                ini_set('display_errors', $old_display_errors);
                exit;
            }
            
            $provinces = $this->provinceModel->getByCountryForDropdown($countryId);
            $response = [
                'success' => true,
                'data' => $provinces
            ];
            
            // Clean output and send only JSON
            ob_clean();
            echo json_encode($response);
            
            // Restore error display setting
            ini_set('display_errors', $old_display_errors);
            exit;
            
        } catch (\Exception $e) {
            error_log('LocationsController::getProvinces - ' . $e->getMessage());
            
            $response = [
                'success' => false,
                'message' => 'Error fetching provinces'
            ];
            
            ob_clean();
            echo json_encode($response);
            
            // Restore error display setting
            ini_set('display_errors', $old_display_errors);
            exit;
        }
    }

    /**
     * Get districts by province (API)
     */
    public function getDistricts($provinceId)
    {
        // Temporarily disable error display to prevent HTML contamination
        $old_display_errors = ini_get('display_errors');
        ini_set('display_errors', '0');
        
        // Clean any output buffers to prevent HTML contamination
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        // Start clean output buffering
        ob_start();
        
        // Set headers directly
        header('Content-Type: application/json');
        header('Cache-Control: no-cache, must-revalidate');

        try {
            // Validate province ID
            if (!$provinceId || !is_numeric($provinceId)) {
                $response = [
                    'success' => false,
                    'message' => 'Invalid province ID provided'
                ];
                
                ob_clean();
                echo json_encode($response);
                
                // Restore error display setting
                ini_set('display_errors', $old_display_errors);
                exit;
            }
            
            $districts = $this->districtModel->getByProvinceForDropdown($provinceId);
            $response = [
                'success' => true,
                'data' => $districts
            ];
            
            // Clean output and send only JSON
            ob_clean();
            echo json_encode($response);
            
            // Restore error display setting
            ini_set('display_errors', $old_display_errors);
            exit;
            
        } catch (\Exception $e) {
            error_log('LocationsController::getDistricts - ' . $e->getMessage());
            
            $response = [
                'success' => false,
                'message' => 'Error fetching districts'
            ];
            
            ob_clean();
            echo json_encode($response);
            
            // Restore error display setting
            ini_set('display_errors', $old_display_errors);
            exit;
        }
    }

    /**
     * Get LLGs by district (API)
     */
    public function getLlgs($districtId)
    {
        // Temporarily disable error display to prevent HTML contamination
        $old_display_errors = ini_get('display_errors');
        ini_set('display_errors', '0');
        
        // Clean any output buffers to prevent HTML contamination
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        // Start clean output buffering
        ob_start();
        
        // Set headers directly
        header('Content-Type: application/json');
        header('Cache-Control: no-cache, must-revalidate');

        try {
            // Validate district ID
            if (!$districtId || !is_numeric($districtId)) {
                $response = [
                    'success' => false,
                    'message' => 'Invalid district ID provided'
                ];
                
                ob_clean();
                echo json_encode($response);
                
                // Restore error display setting
                ini_set('display_errors', $old_display_errors);
                exit;
            }
            
            $llgs = $this->llgModel->getByDistrictForDropdown($districtId);
            $response = [
                'success' => true,
                'data' => $llgs
            ];
            
            // Clean output and send only JSON
            ob_clean();
            echo json_encode($response);
            
            // Restore error display setting
            ini_set('display_errors', $old_display_errors);
            exit;
            
        } catch (\Exception $e) {
            error_log('LocationsController::getLlgs - ' . $e->getMessage());
            
            $response = [
                'success' => false,
                'message' => 'Error fetching LLGs'
            ];
            
            ob_clean();
            echo json_encode($response);
            
            // Restore error display setting
            ini_set('display_errors', $old_display_errors);
            exit;
        }
    }
}
