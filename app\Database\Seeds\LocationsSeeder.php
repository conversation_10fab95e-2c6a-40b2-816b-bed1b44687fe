<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class LocationsSeeder extends Seeder
{
    public function run()
    {
        // Insert Countries
        $countries = [
            ['name' => 'Papua New Guinea', 'code' => 'PNG', 'created_at' => date('Y-m-d H:i:s')],
            ['name' => 'Australia', 'code' => 'AUS', 'created_at' => date('Y-m-d H:i:s')],
            ['name' => 'Solomon Islands', 'code' => 'SLB', 'created_at' => date('Y-m-d H:i:s')]
        ];
        
        $this->db->table('countries')->insertBatch($countries);
        
        // Get PNG country ID for provinces
        $pngCountry = $this->db->table('countries')->where('code', 'PNG')->get()->getRow();
        
        if ($pngCountry) {
            // Insert Provinces for PNG
            $provinces = [
                ['country_id' => $pngCountry->id, 'name' => 'Western Province', 'code' => 'WP', 'created_at' => date('Y-m-d H:i:s')],
                ['country_id' => $pngCountry->id, 'name' => 'Southern Highlands Province', 'code' => 'SHP', 'created_at' => date('Y-m-d H:i:s')],
                ['country_id' => $pngCountry->id, 'name' => 'National Capital District', 'code' => 'NCD', 'created_at' => date('Y-m-d H:i:s')],
                ['country_id' => $pngCountry->id, 'name' => 'Central Province', 'code' => 'CP', 'created_at' => date('Y-m-d H:i:s')]
            ];
            
            $this->db->table('provinces')->insertBatch($provinces);
            
            // Get Western Province for districts
            $westernProvince = $this->db->table('provinces')->where('code', 'WP')->get()->getRow();
            
            if ($westernProvince) {
                // Insert Districts for Western Province
                $districts = [
                    ['province_id' => $westernProvince->id, 'name' => 'South Fly District', 'code' => 'SFD', 'created_at' => date('Y-m-d H:i:s')],
                    ['province_id' => $westernProvince->id, 'name' => 'North Fly District', 'code' => 'NFD', 'created_at' => date('Y-m-d H:i:s')],
                    ['province_id' => $westernProvince->id, 'name' => 'Middle Fly District', 'code' => 'MFD', 'created_at' => date('Y-m-d H:i:s')]
                ];
                
                $this->db->table('districts')->insertBatch($districts);
                
                // Get South Fly District for LLGs
                $southFlyDistrict = $this->db->table('districts')->where('code', 'SFD')->get()->getRow();
                
                if ($southFlyDistrict) {
                    // Insert LLGs for South Fly District
                    $llgs = [
                        ['district_id' => $southFlyDistrict->id, 'name' => 'Daru Urban LLG', 'code' => 'DULLG', 'created_at' => date('Y-m-d H:i:s')],
                        ['district_id' => $southFlyDistrict->id, 'name' => 'Kiwai Rural LLG', 'code' => 'KRLLG', 'created_at' => date('Y-m-d H:i:s')],
                        ['district_id' => $southFlyDistrict->id, 'name' => 'Oriomo-Bituri Rural LLG', 'code' => 'OBRLLG', 'created_at' => date('Y-m-d H:i:s')]
                    ];
                    
                    $this->db->table('llgs')->insertBatch($llgs);
                    
                    // Get Daru Urban LLG for sample locations
                    $daruLlg = $this->db->table('llgs')->where('code', 'DULLG')->get()->getRow();
                    
                    if ($daruLlg) {
                        // Insert sample locations
                        $locations = [
                            [
                                'country_id' => $pngCountry->id,
                                'province_id' => $westernProvince->id,
                                'district_id' => $southFlyDistrict->id,
                                'llg_id' => $daruLlg->id,
                                'ward' => 'Ward 1',
                                'location_name' => 'Daru Town Center',
                                'gps_latitude' => -9.0765,
                                'gps_longitude' => 143.2092,
                                'remarks' => 'Main commercial area of Daru Island',
                                'created_by' => 1, // Assuming admin user ID is 1
                                'created_at' => date('Y-m-d H:i:s'),
                                'is_deleted' => false
                            ],
                            [
                                'country_id' => $pngCountry->id,
                                'province_id' => $westernProvince->id,
                                'district_id' => $southFlyDistrict->id,
                                'llg_id' => $daruLlg->id,
                                'ward' => 'Ward 2',
                                'location_name' => 'Daru Airport Area',
                                'gps_latitude' => -9.0836,
                                'gps_longitude' => 143.2081,
                                'remarks' => 'Near Daru Airport terminal',
                                'created_by' => 1,
                                'created_at' => date('Y-m-d H:i:s'),
                                'is_deleted' => false
                            ],
                            [
                                'country_id' => $pngCountry->id,
                                'province_id' => $westernProvince->id,
                                'district_id' => $southFlyDistrict->id,
                                'llg_id' => $daruLlg->id,
                                'ward' => 'Ward 3',
                                'location_name' => 'Daru Market',
                                'gps_latitude' => -9.0751,
                                'gps_longitude' => 143.2105,
                                'remarks' => 'Central market area for local trade',
                                'created_by' => 1,
                                'created_at' => date('Y-m-d H:i:s'),
                                'is_deleted' => false
                            ]
                        ];
                        
                        $this->db->table('locations')->insertBatch($locations);
                    }
                }
            }
            
            // Add some data for NCD as well
            $ncdProvince = $this->db->table('provinces')->where('code', 'NCD')->get()->getRow();
            
            if ($ncdProvince) {
                // Insert Districts for NCD
                $ncdDistricts = [
                    ['province_id' => $ncdProvince->id, 'name' => 'Moresby North-East District', 'code' => 'MNED', 'created_at' => date('Y-m-d H:i:s')],
                    ['province_id' => $ncdProvince->id, 'name' => 'Moresby North-West District', 'code' => 'MNWD', 'created_at' => date('Y-m-d H:i:s')],
                    ['province_id' => $ncdProvince->id, 'name' => 'Moresby South District', 'code' => 'MSD', 'created_at' => date('Y-m-d H:i:s')]
                ];
                
                $this->db->table('districts')->insertBatch($ncdDistricts);
                
                // Get Moresby North-East District for LLGs
                $mnedDistrict = $this->db->table('districts')->where('code', 'MNED')->get()->getRow();
                
                if ($mnedDistrict) {
                    // Insert LLGs for Moresby North-East District
                    $mnedLlgs = [
                        ['district_id' => $mnedDistrict->id, 'name' => 'Port Moresby North-East LLG', 'code' => 'PMNELLG', 'created_at' => date('Y-m-d H:i:s')],
                        ['district_id' => $mnedDistrict->id, 'name' => 'Laloki-Pitililu Rural LLG', 'code' => 'LPRLLG', 'created_at' => date('Y-m-d H:i:s')]
                    ];
                    
                    $this->db->table('llgs')->insertBatch($mnedLlgs);
                    
                    // Get Port Moresby North-East LLG for sample locations
                    $pmneLlg = $this->db->table('llgs')->where('code', 'PMNELLG')->get()->getRow();
                    
                    if ($pmneLlg) {
                        // Insert sample locations for Port Moresby
                        $pmLocations = [
                            [
                                'country_id' => $pngCountry->id,
                                'province_id' => $ncdProvince->id,
                                'district_id' => $mnedDistrict->id,
                                'llg_id' => $pmneLlg->id,
                                'ward' => 'Ward 1',
                                'location_name' => 'Downtown Port Moresby',
                                'gps_latitude' => -9.4438,
                                'gps_longitude' => 147.1803,
                                'remarks' => 'Central business district',
                                'created_by' => 1,
                                'created_at' => date('Y-m-d H:i:s'),
                                'is_deleted' => false
                            ],
                            [
                                'country_id' => $pngCountry->id,
                                'province_id' => $ncdProvince->id,
                                'district_id' => $mnedDistrict->id,
                                'llg_id' => $pmneLlg->id,
                                'ward' => 'Ward 5',
                                'location_name' => 'Boroko Shopping Center',
                                'gps_latitude' => -9.4647,
                                'gps_longitude' => 147.1569,
                                'remarks' => 'Major shopping and commercial area',
                                'created_by' => 1,
                                'created_at' => date('Y-m-d H:i:s'),
                                'is_deleted' => false
                            ]
                        ];
                        
                        $this->db->table('locations')->insertBatch($pmLocations);
                    }
                }
            }
        }
        
        echo "Locations seeder completed successfully!\n";
        echo "Added sample data for Papua New Guinea with provinces, districts, LLGs, and locations.\n";
    }
}
