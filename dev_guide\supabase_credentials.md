Project url: https://ziiwdowgicworjaixldn.supabase.co
API Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InppaXdkb3dnaWN3b3JqYWl4bGRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg0Nzc1NzIsImV4cCI6MjA3NDA1MzU3Mn0.xoNnzOjWb05U22ntYgA31U3EeJcFUJ7RPpHmtp1Z-FM
Javascrpt code: 

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://ziiwdowgicworjaixldn.supabase.co'
const supabaseKey = process.env.SUPABASE_KEY
const supabase = createClient(supabaseUrl, supabaseKey)

PHP:
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.ziiwdowgicworjaixldn.supabase.co:5432/postgres
Password: Dakoii@2025

user=postgres.ziiwdowgicworjaixldn 
password=[YOUR-PASSWORD] 
host=aws-1-ap-southeast-1.pooler.supabase.com
port=6543
dbname=postgres

user=postgres.ziiwdowgicworjaixldn 
password=[YOUR-PASSWORD] 
host=aws-1-ap-southeast-1.pooler.supabase.com
port=5432
dbname=postgres

Direct connection
Ideal for applications with persistent, long-lived connections, such as those running on virtual machines or long-standing containers.

.env
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.ziiwdowgicworjaixldn.supabase.co:5432/postgres


View parameters
Suitable for long-lived, persistent connections
Each client has a dedicated connection to Postgres
Not IPv4 compatible
Use Session Pooler if on a IPv4 network or purchase IPv4 add-on
IPv4 add-on
Pooler settings

Some platforms are IPv4-only:
Transaction pooler
Shared Pooler
Ideal for stateless applications like serverless functions where each interaction with Postgres is brief and isolated.

.env
user=postgres.ziiwdowgicworjaixldn 
password=[YOUR-PASSWORD] 
host=aws-1-ap-southeast-1.pooler.supabase.com
port=6543
dbname=postgres

Does not support PREPARE statements


View parameters
Suitable for a large number of connected clients
Clients share a connection pool
IPv4 compatible
Transaction pooler connections are IPv4 proxied for free.
Session pooler
Shared Pooler
Only recommended as an alternative to Direct Connection, when connecting via an IPv4 network.

.env
user=postgres.ziiwdowgicworjaixldn 
password=[YOUR-PASSWORD] 
host=aws-1-ap-southeast-1.pooler.supabase.com
port=5432
dbname=postgres


DB Password: Dakoii@2025