<?php

namespace App\Models;

use CodeIgniter\Model;

class LocationModel extends Model
{
    protected $table = 'locations';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'country_id',
        'province_id',
        'district_id',
        'llg_id',
        'ward',
        'location_name',
        'gps_latitude',
        'gps_longitude',
        'remarks',
        'is_deleted',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'country_id' => 'required|integer|is_not_unique[countries.id]',
        'province_id' => 'required|integer|is_not_unique[provinces.id]',
        'district_id' => 'required|integer|is_not_unique[districts.id]',
        'llg_id' => 'required|integer|is_not_unique[llgs.id]',
        'ward' => 'required|min_length[2]|max_length[100]',
        'location_name' => 'required|min_length[2]|max_length[100]',
        'gps_latitude' => 'permit_empty|decimal',
        'gps_longitude' => 'permit_empty|decimal',
        'remarks' => 'permit_empty|max_length[1000]',
        'created_by' => 'required|integer|is_not_unique[users.id]'
    ];

    protected $validationMessages = [
        'country_id' => [
            'required' => 'Country is required.',
            'integer' => 'Country must be a valid selection.',
            'is_not_unique' => 'Selected country does not exist.'
        ],
        'province_id' => [
            'required' => 'Province is required.',
            'integer' => 'Province must be a valid selection.',
            'is_not_unique' => 'Selected province does not exist.'
        ],
        'district_id' => [
            'required' => 'District is required.',
            'integer' => 'District must be a valid selection.',
            'is_not_unique' => 'Selected district does not exist.'
        ],
        'llg_id' => [
            'required' => 'LLG is required.',
            'integer' => 'LLG must be a valid selection.',
            'is_not_unique' => 'Selected LLG does not exist.'
        ],
        'ward' => [
            'required' => 'Ward is required.',
            'min_length' => 'Ward must be at least 2 characters long.',
            'max_length' => 'Ward cannot exceed 100 characters.'
        ],
        'location_name' => [
            'required' => 'Location name is required.',
            'min_length' => 'Location name must be at least 2 characters long.',
            'max_length' => 'Location name cannot exceed 100 characters.'
        ],
        'gps_latitude' => [
            'decimal' => 'GPS latitude must be a valid decimal number.'
        ],
        'gps_longitude' => [
            'decimal' => 'GPS longitude must be a valid decimal number.'
        ],
        'remarks' => [
            'max_length' => 'Remarks cannot exceed 1000 characters.'
        ],
        'created_by' => [
            'required' => 'Created by user is required.',
            'integer' => 'Created by must be a valid user.',
            'is_not_unique' => 'Created by user does not exist.'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['setCreatedBy'];
    protected $afterInsert = [];
    protected $beforeUpdate = ['setUpdatedBy'];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = ['setDeletedBy'];
    protected $afterDelete = [];

    /**
     * Get all locations for dropdown
     */
    public function getForDropdown(): array
    {
        return $this->select('id, location_name, ward')
                    ->where('is_deleted', false)
                    ->orderBy('location_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get all locations with hierarchy for dropdown
     */
    public function getLocationsForDropdown(): array
    {
        $locations = $this->select('locations.id,
                                   locations.location_name,
                                   CONCAT(locations.location_name, \' (\',
                                          countries.name, \' > \',
                                          provinces.name, \' > \',
                                          districts.name, \' > \',
                                          llgs.name, \')\') as hierarchy')
                        ->join('countries', 'countries.id = locations.country_id')
                        ->join('provinces', 'provinces.id = locations.province_id')
                        ->join('districts', 'districts.id = locations.district_id')
                        ->join('llgs', 'llgs.id = locations.llg_id')
                        ->where('locations.is_deleted', false)
                        ->orderBy('countries.name, provinces.name, districts.name, llgs.name, locations.location_name')
                        ->findAll();

        return $locations;
    }

    /**
     * Get locations with full hierarchy information
     */
    public function getLocationsWithHierarchy(int $perPage = 20, string $search = '', array $filters = []): array
    {
        // Use caching for better performance
        $cacheHelper = new \App\Libraries\CacheHelper();
        $cacheKey = $cacheHelper->generatePaginationKey('locations_hierarchy', [
            'perPage' => $perPage,
            'search' => $search,
            'filters' => $filters,
            'page' => request()->getGet('page') ?? 1
        ]);

        return $cacheHelper->remember($cacheKey, 300, function() use ($perPage, $search, $filters) {
            $builder = $this->select('locations.*,
                                     countries.name as country_name,
                                     provinces.name as province_name,
                                     districts.name as district_name,
                                     llgs.name as llg_name,
                                     users.fullname as created_by_name')
                            ->join('countries', 'countries.id = locations.country_id')
                            ->join('provinces', 'provinces.id = locations.province_id')
                            ->join('districts', 'districts.id = locations.district_id')
                            ->join('llgs', 'llgs.id = locations.llg_id')
                            ->join('users', 'users.id = locations.created_by')
                            ->where('locations.is_deleted', false);

            if (!empty($search)) {
                $builder->groupStart()
                        ->like('locations.location_name', $search)
                        ->orLike('locations.ward', $search)
                        ->orLike('countries.name', $search)
                        ->orLike('provinces.name', $search)
                        ->orLike('districts.name', $search)
                        ->orLike('llgs.name', $search)
                        ->groupEnd();
            }

            // Apply filters
            if (!empty($filters['country_id'])) {
                $builder->where('locations.country_id', $filters['country_id']);
            }
            if (!empty($filters['province_id'])) {
                $builder->where('locations.province_id', $filters['province_id']);
            }
            if (!empty($filters['district_id'])) {
                $builder->where('locations.district_id', $filters['district_id']);
            }
            if (!empty($filters['llg_id'])) {
                $builder->where('locations.llg_id', $filters['llg_id']);
            }

            return $builder->orderBy('countries.name, provinces.name, districts.name, llgs.name, locations.location_name', 'ASC')
                          ->paginate($perPage);
        });
    }

    /**
     * Get location with full hierarchy details
     */
    public function getLocationWithHierarchy(int $id): ?array
    {
        // Use caching for individual location details
        $cacheHelper = new \App\Libraries\CacheHelper();
        $cacheKey = "location_hierarchy_{$id}";

        return $cacheHelper->remember($cacheKey, 600, function() use ($id) {
            return $this->select('locations.*,
                                 countries.name as country_name, countries.code as country_code,
                                 provinces.name as province_name,
                                 districts.name as district_name,
                                 llgs.name as llg_name,
                                 users.fullname as created_by_name,
                                 updater.fullname as updated_by_name')
                        ->join('countries', 'countries.id = locations.country_id')
                        ->join('provinces', 'provinces.id = locations.province_id')
                        ->join('districts', 'districts.id = locations.district_id')
                        ->join('llgs', 'llgs.id = locations.llg_id')
                        ->join('users', 'users.id = locations.created_by')
                        ->join('users as updater', 'updater.id = locations.updated_by', 'left')
                        ->where('locations.id', $id)
                        ->where('locations.is_deleted', false)
                        ->first();
        });
    }

    /**
     * Create a new location
     */
    public function createLocation(array $data, int $createdBy): bool
    {
        $data['created_by'] = $createdBy;
        return $this->insert($data) !== false;
    }

    /**
     * Update location data
     */
    public function updateLocation(int $id, array $data, int $updatedBy): bool
    {
        $data['updated_by'] = $updatedBy;
        return $this->update($id, $data);
    }

    /**
     * Get location statistics
     */
    public function getLocationStats(): array
    {
        return [
            'total_locations' => $this->where('is_deleted', false)->countAllResults(),
            'recent_locations' => $this->where('is_deleted', false)
                                      ->where('created_at >=', date('Y-m-d H:i:s', strtotime('-30 days')))
                                      ->countAllResults(),
            'locations_with_gps' => $this->where('is_deleted', false)
                                        ->where('gps_latitude IS NOT NULL')
                                        ->where('gps_longitude IS NOT NULL')
                                        ->countAllResults()
        ];
    }

    /**
     * Callback: Set created_by before insert
     */
    protected function setCreatedBy(array $data): array
    {
        if (isset($data['data']['created_by'])) {
            return $data;
        }
        
        $userId = session()->get('user_id');
        if ($userId) {
            $data['data']['created_by'] = $userId;
        }
        
        return $data;
    }

    /**
     * Callback: Set updated_by before update
     */
    protected function setUpdatedBy(array $data): array
    {
        $userId = session()->get('user_id');
        if ($userId) {
            $data['data']['updated_by'] = $userId;
        }
        
        return $data;
    }

    /**
     * Callback: Set deleted_by before delete
     */
    protected function setDeletedBy(array $data): array
    {
        $userId = session()->get('user_id');
        if ($userId) {
            $data['data']['deleted_by'] = $userId;
            $data['data']['is_deleted'] = true;
        }
        
        return $data;
    }

    /**
     * Soft delete override
     */
    public function delete($id = null, bool $purge = false)
    {
        $userId = session()->get('user_id');
        
        if ($purge) {
            return parent::delete($id, true);
        }
        
        return $this->update($id, [
            'is_deleted' => true,
            'deleted_by' => $userId
        ]);
    }
}
