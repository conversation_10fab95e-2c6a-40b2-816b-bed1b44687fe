# DCBuyer CodeIgniter 4 RESTful System Architecture

## 1. Overview

DCBuyer is a web-based commodity trading and purchasing management system built using CodeIgniter 4 framework with Supabase PostgreSQL database. The system provides role-based access control, comprehensive transaction management, and robust reporting capabilities for agricultural marketplaces.

### 1.1 Purpose
- Streamline commodity purchasing processes through web-based interface
- Provide role-based access control with session-based authentication
- Enable efficient transaction processing and management
- Maintain comprehensive audit trails and transaction records
- Support multi-user collaboration with role-based permissions

### 1.2 Scope
The system covers user management, customer management, commodity management, buyer assignments, and transaction processing using a traditional MVC web application architecture with CodeIgniter 4 and Supabase PostgreSQL.

### 1.3 Architecture Philosophy
- **Web-First**: Traditional web application accessible via browsers
- **MVC Architecture**: Clean separation of concerns using CodeIgniter 4 pattern
- **Database-Centric**: Centralized data storage with Supabase PostgreSQL
- **Session-Based Auth**: Secure server-side session management
- **Responsive Design**: Mobile-friendly interface using Bootstrap 5

## 2. System Architecture

### 2.1 High-Level Architecture (CodeIgniter 4 + Supabase)
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │   Mobile Web    │    │   Admin Panel   │
│   (Desktop)     │    │   (Responsive)  │    │   (Bootstrap)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │ HTTP/HTTPS
         ┌─────────────────────────────────────────────────┐
         │           CodeIgniter 4 Web Application         │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │Controllers  │  │   Models    │  │  Views   ││
         │  │(Business    │  │(Data Layer) │  │(UI Layer)││
         │  │ Logic)      │  │             │  │          ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │   Routes    │  │ Libraries   │  │ Helpers  ││
         │  │(URL Mapping)│  │(Services)   │  │(Utilities)││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │            Supabase PostgreSQL Database         │
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │    Users    │  │  Customers  │  │Commodities││
         │  │   Table     │  │   Table     │  │  Table   ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         │  ┌─────────────┐  ┌─────────────┐  ┌──────────┐│
         │  │Transactions │  │Assignments  │  │ Reports  ││
         │  │   Table     │  │   Table     │  │  Table   ││
         │  └─────────────┘  └─────────────┘  └──────────┘│
         └─────────────────────────────────────────────────┘
```

### 2.2 Technology Stack

**Backend Framework:**
- **CodeIgniter 4**: PHP 8.1+ MVC framework
- **PHP**: Server-side scripting language (version 8.1+)
- **Apache/Nginx**: Web server for hosting
- **Composer**: PHP dependency management

**Database:**
- **Supabase PostgreSQL**: Cloud-hosted PostgreSQL database
- **PostgreSQL**: Relational database management system
- **Database Migrations**: CodeIgniter 4 migration system
- **Query Builder**: CodeIgniter 4 database abstraction layer

**Frontend:**
- **HTML5**: Semantic markup with accessibility features
- **CSS3**: Modern styling with Bootstrap 5 framework
- **JavaScript**: Client-side interactivity (ES6+)
- **Bootstrap 5**: Responsive CSS framework
- **jQuery**: JavaScript library for DOM manipulation

**Authentication & Security:**
- **CodeIgniter Sessions**: Server-side session management
- **Password Hashing**: PHP password_hash() function
- **CSRF Protection**: CodeIgniter 4 built-in CSRF protection
- **Input Validation**: Server-side form validation
- **SQL Injection Prevention**: Query builder and prepared statements

**Development Tools:**
- **XAMPP**: Local development environment
- **Composer**: PHP package manager
- **Git**: Version control system
- **VS Code**: Code editor with PHP extensions
- **Browser DevTools**: Debugging and testing

**Deployment:**
- **Web Hosting**: Traditional web hosting with PHP support
- **Database Hosting**: Supabase cloud PostgreSQL
- **File Storage**: Server file system or cloud storage
- **SSL/TLS**: HTTPS encryption for security

## 3. Supabase PostgreSQL Database Design

### 3.1 Database Schema Structure
```
dcbuyers_db (PostgreSQL)
├── users
│   ├── id (bigint, primary key)
│   ├── fullname (text)
│   ├── username (text, unique)
│   ├── email (text, unique)
│   ├── password_hash (varchar)
│   ├── is_admin (boolean)
│   ├── is_buyer (boolean)
│   ├── is_supervisor (boolean)
│   ├── reports_to (bigint, foreign key)
│   ├── status (user_status enum)
│   ├── remarks (text)
│   ├── created_by (bigint)
│   ├── created_at (timestamp)
│   ├── updated_by (bigint)
│   ├── updated_at (timestamp)
│   ├── deleted_by (bigint)
│   └── deleted_at (timestamp)
├── customers (planned)
├── commodities (planned)
├── assignments (planned)
├── transactions (planned)
└── reports (planned)
```

### 3.2 Database Table Schemas

#### Users Table
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    fullname TEXT NOT NULL,
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    is_admin BOOLEAN NOT NULL DEFAULT FALSE,
    is_buyer BOOLEAN NOT NULL DEFAULT FALSE,
    is_supervisor BOOLEAN NOT NULL DEFAULT FALSE,
    reports_to BIGINT REFERENCES users(id),
    status user_status NOT NULL DEFAULT 'active',
    remarks TEXT,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_by BIGINT REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_by BIGINT REFERENCES users(id),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- User status enum
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'suspended', 'pending');

-- Indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_reports_to ON users(reports_to);
```

#### Customers Table (Planned)
```sql
CREATE TABLE customers (
    id BIGSERIAL PRIMARY KEY,
    unique_id VARCHAR(50) NOT NULL UNIQUE,
    name TEXT NOT NULL,
    contact_person TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    country VARCHAR(100),
    postal_code VARCHAR(20),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    credit_limit DECIMAL(15, 2) DEFAULT 0,
    payment_terms VARCHAR(50),
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    notes TEXT,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_by BIGINT REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_by BIGINT REFERENCES users(id),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes
CREATE INDEX idx_customers_unique_id ON customers(unique_id);
CREATE INDEX idx_customers_name ON customers(name);
CREATE INDEX idx_customers_status ON customers(status);
CREATE INDEX idx_customers_location ON customers USING GIST (point(longitude, latitude));
```

#### Commodities Table (Planned)
```sql
CREATE TABLE commodities (
    id BIGSERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    category VARCHAR(100),
    unit_of_measurement VARCHAR(50) NOT NULL,
    base_price DECIMAL(12, 4),
    currency VARCHAR(10) DEFAULT 'PGK',
    grade VARCHAR(20),
    specifications JSONB,
    available_quantity DECIMAL(12, 4) DEFAULT 0,
    reserved_quantity DECIMAL(12, 4) DEFAULT 0,
    minimum_order DECIMAL(12, 4) DEFAULT 0,
    maximum_order DECIMAL(12, 4),
    seasonality VARCHAR(100),
    storage_requirements TEXT,
    quality_standards TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    is_available BOOLEAN NOT NULL DEFAULT TRUE,
    image_url TEXT,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_by BIGINT REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_by BIGINT REFERENCES users(id),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Indexes
CREATE INDEX idx_commodities_name ON commodities(name);
CREATE INDEX idx_commodities_category ON commodities(category);
CREATE INDEX idx_commodities_status ON commodities(status);
CREATE INDEX idx_commodities_available ON commodities(is_available);
```

#### Buyer Commodity Assignments Table (Planned)
```sql
CREATE TABLE buyer_commodity_assignments (
    id BIGSERIAL PRIMARY KEY,
    buyer_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    commodity_id BIGINT NOT NULL REFERENCES commodities(id) ON DELETE CASCADE,

    -- Assignment details
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    assigned_by BIGINT NOT NULL REFERENCES users(id),
    effective_from DATE DEFAULT CURRENT_DATE,
    effective_to DATE,
    is_active BOOLEAN DEFAULT TRUE,
    territory VARCHAR(100),

    -- Purchase limits
    daily_purchase_limit DECIMAL(15, 2),
    weekly_purchase_limit DECIMAL(15, 2),
    monthly_purchase_limit DECIMAL(15, 2),
    max_transaction_amount DECIMAL(15, 2),
    min_transaction_amount DECIMAL(15, 2),

    -- Performance tracking
    total_purchases_amount DECIMAL(15, 2) DEFAULT 0,
    total_transactions_count INTEGER DEFAULT 0,
    last_transaction_date TIMESTAMP WITH TIME ZONE,

    -- Metadata
    notes TEXT,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_by BIGINT REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

    UNIQUE(buyer_id, commodity_id, effective_from)
);

-- Indexes
CREATE INDEX idx_assignments_buyer_id ON buyer_commodity_assignments(buyer_id);
CREATE INDEX idx_assignments_commodity_id ON buyer_commodity_assignments(commodity_id);
CREATE INDEX idx_assignments_active ON buyer_commodity_assignments(is_active);
CREATE INDEX idx_assignments_effective ON buyer_commodity_assignments(effective_from, effective_to);
```

#### Transactions Table (Planned)
```sql
CREATE TABLE transactions (
    id BIGSERIAL PRIMARY KEY,
    transaction_number VARCHAR(50) UNIQUE NOT NULL,

    -- Parties involved
    buyer_id BIGINT NOT NULL REFERENCES users(id),
    customer_id BIGINT NOT NULL REFERENCES customers(id),
    commodity_id BIGINT NOT NULL REFERENCES commodities(id),

    -- Transaction details
    quantity DECIMAL(12, 4) NOT NULL,
    unit VARCHAR(20) NOT NULL,
    unit_price DECIMAL(12, 4) NOT NULL,
    subtotal DECIMAL(15, 2) NOT NULL,
    tax_amount DECIMAL(15, 2) DEFAULT 0,
    discount_amount DECIMAL(15, 2) DEFAULT 0,
    total_amount DECIMAL(15, 2) NOT NULL,

    -- Payment information
    amount_paid DECIMAL(15, 2) NOT NULL,
    balance_due DECIMAL(15, 2) DEFAULT 0,
    payment_method VARCHAR(30),
    payment_reference VARCHAR(100),
    payment_status VARCHAR(20) DEFAULT 'completed',

    -- Quality and grading
    quality_grade VARCHAR(10),
    quality_notes TEXT,
    moisture_content DECIMAL(5, 2),
    purity_percentage DECIMAL(5, 2),
    quality_checked_by BIGINT REFERENCES users(id),
    quality_checked_at TIMESTAMP WITH TIME ZONE,

    -- Location and timing
    transaction_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    location_name VARCHAR(200),

    -- Status and workflow
    status VARCHAR(20) DEFAULT 'draft',
    approval_status VARCHAR(20) DEFAULT 'pending',
    approved_by BIGINT REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,

    -- Additional fields
    notes TEXT,
    internal_notes TEXT,
    created_by BIGINT REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_by BIGINT REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),

    -- Constraints
    CONSTRAINT transactions_quantity_positive CHECK (quantity > 0),
    CONSTRAINT transactions_unit_price_positive CHECK (unit_price > 0),
    CONSTRAINT transactions_total_positive CHECK (total_amount > 0)
);

-- Indexes
CREATE INDEX idx_transactions_number ON transactions(transaction_number);
CREATE INDEX idx_transactions_buyer_id ON transactions(buyer_id);
CREATE INDEX idx_transactions_customer_id ON transactions(customer_id);
CREATE INDEX idx_transactions_commodity_id ON transactions(commodity_id);
CREATE INDEX idx_transactions_date ON transactions(transaction_date);
CREATE INDEX idx_transactions_status ON transactions(status);
```



#### Supporting Tables (Planned)

##### File Attachments Table
```sql
CREATE TABLE file_attachments (
    id BIGSERIAL PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL, -- transaction, customer, commodity, user
    entity_id BIGINT NOT NULL,
    file_type VARCHAR(50) NOT NULL, -- receipt, photo, document, signature
    file_name VARCHAR(255) NOT NULL,
    original_file_name VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_path TEXT NOT NULL,
    file_url TEXT,

    -- Metadata
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    uploaded_by BIGINT NOT NULL REFERENCES users(id),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_file_attachments_entity ON file_attachments(entity_type, entity_id);
CREATE INDEX idx_file_attachments_type ON file_attachments(file_type);
CREATE INDEX idx_file_attachments_uploaded_by ON file_attachments(uploaded_by);
```

##### Audit Logs Table
```sql
CREATE TABLE audit_logs (
    id BIGSERIAL PRIMARY KEY,
    table_name VARCHAR(50) NOT NULL,
    record_id BIGINT NOT NULL,
    action VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[], -- Array of changed field names

    -- Actor information
    user_id BIGINT REFERENCES users(id),
    user_email VARCHAR(100),
    user_role VARCHAR(50),

    -- Request context
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(100),

    -- Timing
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Additional context
    reason TEXT, -- Why the change was made
    notes TEXT
);

CREATE INDEX idx_audit_logs_table_record ON audit_logs(table_name, record_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

## 4. CodeIgniter 4 System Components

### 4.1 Authentication & Authorization Service
**Technology**: CodeIgniter 4 Sessions + PHP
- **Session-based authentication** with secure server-side sessions
- **Role-based access control (RBAC)** with database-driven permissions
- **Multi-role support** per user with boolean flags (is_admin, is_buyer, is_supervisor)
- **Session management** with CodeIgniter 4 session library
- **Password security** with PHP password_hash() and password_verify()
- **CSRF protection** with CodeIgniter 4 built-in CSRF tokens
- **Input validation** with CodeIgniter 4 validation library

### 4.2 User Management Service (RESTful)
**Technology**: CodeIgniter 4 RESTful Controllers + Models
- **RESTful CRUD operations** with proper HTTP methods (GET, POST, PUT, PATCH, DELETE)
- **JSON API responses** with standardized format
- **Role assignment/management** via dedicated API endpoints
- **User profile management** with file upload API endpoints
- **Activity logging** with audit trail functionality
- **Advanced filtering and search** with query parameters
- **Hierarchical user structure** with reports_to relationships

### 4.3 Customer Management Service (Planned)
**Technology**: CodeIgniter 4 MVC + Supabase
- **Customer registration** with server-side validation
- **Unique customer ID generation** with database sequences
- **Contact information management** with relational database
- **Geographic location tracking** with latitude/longitude fields
- **Document management** with file upload system
- **Customer search** with SQL queries and indexing
- **Credit limit management** and payment terms tracking

### 4.4 Commodity Management Service (Planned)
**Technology**: CodeIgniter 4 MVC + Supabase
- **Commodity catalog management** with database tables
- **Unit of measurement** standardization
- **Dynamic pricing** with database updates
- **Quality specifications** with JSONB fields
- **Seasonal availability** tracking with date fields
- **Image gallery management** with file system storage
- **Inventory tracking** with database counters

### 4.5 Assignment Management Service (Planned)
**Technology**: CodeIgniter 4 MVC + Supabase
- **Buyer-commodity assignment** with relational tables
- **Assignment history tracking** with audit trails
- **Bulk assignment operations** with database transactions
- **Purchase limit management** with validation rules
- **Performance tracking** with aggregated queries
- **Assignment validation** with business logic

### 4.6 Transaction Processing Service (Planned)
**Technology**: CodeIgniter 4 MVC + Supabase
- **Transaction creation** with form-based input
- **Multi-step workflow** using database status fields
- **Payment processing** with server-side calculations
- **Quality assessment** integration with file uploads
- **Receipt generation** with PDF libraries
- **Transaction approval** workflow with role-based permissions
- **Commission calculation** with database triggers

### 4.7 File Management Service (Planned)
**Technology**: CodeIgniter 4 + File System
- **File upload** with server-side validation and processing
- **Image optimization** with PHP image libraries
- **Secure file access** with authentication checks
- **File organization** by entity type and ID
- **Metadata management** with database file records
- **File cleanup** with scheduled maintenance tasks

### 4.8 Reporting & Analytics Service (Planned)
**Technology**: CodeIgniter 4 + Supabase + Chart Libraries
- **Transaction reports** with SQL aggregation queries
- **Performance analytics** with database analytics
- **Financial reports** with server-side calculations
- **Data export** with PHP libraries (TCPDF, PhpSpreadsheet)
- **Dashboard metrics** with cached database queries
- **Custom report builder** with dynamic SQL generation

## 5. CodeIgniter 4 RESTful API & Routing Design

### 5.1 RESTful Architecture Principles
- **MVC pattern** with clear separation of concerns
- **RESTful resource-based URLs** with standard HTTP methods
- **JSON API responses** for data exchange
- **HTTP status codes** for proper response handling
- **Session-based authentication** with API token support
- **CSRF protection** for web forms, API token validation for REST endpoints
- **Input validation** with CodeIgniter 4 validation library
- **Consistent error handling** with standardized JSON responses

### 5.2 RESTful API Endpoints

#### Authentication API
```
POST   /api/auth/login                 # User authentication
POST   /api/auth/logout                # User logout
POST   /api/auth/refresh               # Refresh authentication token
GET    /api/auth/me                    # Get current user profile
```

#### User Management API (RESTful)
```
GET    /api/users                      # List all users (with pagination)
POST   /api/users                      # Create new user
GET    /api/users/(:num)               # Get specific user by ID
PUT    /api/users/(:num)               # Update user (full update)
PATCH  /api/users/(:num)               # Partial user update
DELETE /api/users/(:num)               # Delete/deactivate user
GET    /api/users/(:num)/roles         # Get user roles
POST   /api/users/(:num)/roles         # Assign role to user
DELETE /api/users/(:num)/roles/(:num)  # Remove role from user
```

#### Customer Management API (Planned)
```
GET    /api/customers                  # List customers (with pagination & filters)
POST   /api/customers                  # Create new customer
GET    /api/customers/(:num)           # Get specific customer by ID
PUT    /api/customers/(:num)           # Update customer (full update)
PATCH  /api/customers/(:num)           # Partial customer update
DELETE /api/customers/(:num)           # Delete/deactivate customer
GET    /api/customers/search           # Search customers with query parameters
GET    /api/customers/(:num)/transactions # Get customer transaction history
```

#### Commodity Management API (Planned)
```
GET    /api/commodities                # List commodities (with pagination & filters)
POST   /api/commodities                # Create new commodity
GET    /api/commodities/(:num)         # Get specific commodity by ID
PUT    /api/commodities/(:num)         # Update commodity (full update)
PATCH  /api/commodities/(:num)         # Partial commodity update
DELETE /api/commodities/(:num)         # Delete/deactivate commodity
GET    /api/commodities/categories     # Get commodity categories
GET    /api/commodities/(:num)/assignments # Get commodity buyer assignments
```

#### Assignment Management API (Planned)
```
GET    /api/assignments                # List buyer-commodity assignments
POST   /api/assignments                # Create new assignment
GET    /api/assignments/(:num)         # Get specific assignment by ID
PUT    /api/assignments/(:num)         # Update assignment (full update)
PATCH  /api/assignments/(:num)         # Partial assignment update
DELETE /api/assignments/(:num)         # Delete/deactivate assignment
GET    /api/buyers/(:num)/assignments  # Get assignments for specific buyer
GET    /api/commodities/(:num)/assignments # Get assignments for specific commodity
```

#### Transaction Management API (Planned)
```
GET    /api/transactions               # List transactions (with pagination & filters)
POST   /api/transactions               # Create new transaction
GET    /api/transactions/(:num)        # Get specific transaction by ID
PUT    /api/transactions/(:num)        # Update transaction (full update)
PATCH  /api/transactions/(:num)        # Partial transaction update
DELETE /api/transactions/(:num)        # Cancel/delete transaction
POST   /api/transactions/(:num)/approve # Approve transaction
POST   /api/transactions/(:num)/reject  # Reject transaction
GET    /api/transactions/(:num)/receipt # Generate and download receipt PDF
GET    /api/buyers/(:num)/transactions  # Get transactions for specific buyer
```

#### Reporting API (Planned)
```
GET    /api/reports/transactions       # Transaction reports with filters
GET    /api/reports/buyers             # Buyer performance reports
GET    /api/reports/commodities        # Commodity performance reports
GET    /api/reports/dashboard          # Dashboard summary statistics
POST   /api/reports/export             # Export reports (PDF/Excel/CSV)
GET    /api/reports/(:segment)/download # Download generated report files
```

### 5.3 RESTful API Response Format

#### Success Response Format
```json
{
  "success": true,
  "data": {
    // Response data (single object or array)
  },
  "message": "Operation completed successfully",
  "meta": {
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 100,
      "total_pages": 5,
      "has_next": true,
      "has_previous": false
    },
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

#### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ]
  },
  "meta": {
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

#### HTTP Status Codes
- **200 OK**: Successful GET, PUT, PATCH requests
- **201 Created**: Successful POST requests
- **204 No Content**: Successful DELETE requests
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Access denied
- **404 Not Found**: Resource not found
- **422 Unprocessable Entity**: Validation errors
- **500 Internal Server Error**: Server errors

## 6. RESTful Controller Implementation

### 6.1 Base RESTful Controller Structure
```php
<?php
namespace App\Controllers\Api;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class BaseApiController extends ResourceController
{
    use ResponseTrait;

    protected $format = 'json';
    protected $modelName;

    // GET /api/resource
    public function index()
    {
        $data = $this->model->paginate(20);
        return $this->respond([
            'success' => true,
            'data' => $data,
            'meta' => [
                'pagination' => $this->model->pager->getDetails(),
                'timestamp' => date('c')
            ]
        ]);
    }

    // GET /api/resource/(:num)
    public function show($id = null)
    {
        $data = $this->model->find($id);
        if (!$data) {
            return $this->failNotFound('Resource not found');
        }

        return $this->respond([
            'success' => true,
            'data' => $data,
            'meta' => ['timestamp' => date('c')]
        ]);
    }

    // POST /api/resource
    public function create()
    {
        $rules = $this->getValidationRules();
        if (!$this->validate($rules)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $data = $this->request->getJSON(true);
        $id = $this->model->insert($data);

        return $this->respondCreated([
            'success' => true,
            'data' => $this->model->find($id),
            'message' => 'Resource created successfully'
        ]);
    }

    // PUT /api/resource/(:num)
    public function update($id = null)
    {
        if (!$this->model->find($id)) {
            return $this->failNotFound('Resource not found');
        }

        $rules = $this->getValidationRules();
        if (!$this->validate($rules)) {
            return $this->failValidationErrors($this->validator->getErrors());
        }

        $data = $this->request->getJSON(true);
        $this->model->update($id, $data);

        return $this->respond([
            'success' => true,
            'data' => $this->model->find($id),
            'message' => 'Resource updated successfully'
        ]);
    }

    // DELETE /api/resource/(:num)
    public function delete($id = null)
    {
        if (!$this->model->find($id)) {
            return $this->failNotFound('Resource not found');
        }

        $this->model->delete($id);

        return $this->respondDeleted([
            'success' => true,
            'message' => 'Resource deleted successfully'
        ]);
    }
}
```

### 6.2 User API Controller Example
```php
<?php
namespace App\Controllers\Api;

use App\Models\UserModel;

class Users extends BaseApiController
{
    protected $modelName = UserModel::class;

    protected function getValidationRules()
    {
        return [
            'fullname' => 'required|min_length[2]|max_length[100]',
            'username' => 'required|min_length[3]|max_length[50]|is_unique[users.username,id,{id}]',
            'email' => 'required|valid_email|is_unique[users.email,id,{id}]',
            'password' => 'required|min_length[8]',
            'is_admin' => 'permit_empty|in_list[0,1]',
            'is_buyer' => 'permit_empty|in_list[0,1]',
            'is_supervisor' => 'permit_empty|in_list[0,1]'
        ];
    }

    // GET /api/users/(:num)/roles
    public function roles($userId = null)
    {
        $user = $this->model->find($userId);
        if (!$user) {
            return $this->failNotFound('User not found');
        }

        $roles = [];
        if ($user['is_admin']) $roles[] = 'admin';
        if ($user['is_buyer']) $roles[] = 'buyer';
        if ($user['is_supervisor']) $roles[] = 'supervisor';

        return $this->respond([
            'success' => true,
            'data' => $roles,
            'meta' => ['timestamp' => date('c')]
        ]);
    }
}
```

### 6.3 RESTful Routes Configuration
```php
<?php
// app/Config/Routes.php

// API Routes Group
$routes->group('api', ['namespace' => 'App\Controllers\Api'], function($routes) {

    // Authentication routes
    $routes->post('auth/login', 'Auth::login');
    $routes->post('auth/logout', 'Auth::logout');
    $routes->post('auth/refresh', 'Auth::refresh');
    $routes->get('auth/me', 'Auth::me');

    // RESTful resource routes
    $routes->resource('users', ['controller' => 'Users']);
    $routes->resource('customers', ['controller' => 'Customers']);
    $routes->resource('commodities', ['controller' => 'Commodities']);
    $routes->resource('assignments', ['controller' => 'Assignments']);
    $routes->resource('transactions', ['controller' => 'Transactions']);

    // Custom nested routes
    $routes->get('users/(:num)/roles', 'Users::roles/$1');
    $routes->post('users/(:num)/roles', 'Users::assignRole/$1');
    $routes->delete('users/(:num)/roles/(:num)', 'Users::removeRole/$1/$2');

    $routes->get('buyers/(:num)/assignments', 'Assignments::byBuyer/$1');
    $routes->get('commodities/(:num)/assignments', 'Assignments::byCommodity/$1');

    $routes->post('transactions/(:num)/approve', 'Transactions::approve/$1');
    $routes->post('transactions/(:num)/reject', 'Transactions::reject/$1');
    $routes->get('transactions/(:num)/receipt', 'Transactions::receipt/$1');

    // Reporting routes
    $routes->get('reports/transactions', 'Reports::transactions');
    $routes->get('reports/buyers', 'Reports::buyers');
    $routes->get('reports/commodities', 'Reports::commodities');
    $routes->get('reports/dashboard', 'Reports::dashboard');
    $routes->post('reports/export', 'Reports::export');
});

// Web Routes (for traditional web interface if needed)
$routes->get('/', 'Home::index');
$routes->get('dashboard', 'Dashboard::index', ['filter' => 'auth']);
```

### 6.4 API Middleware and Filters
```php
<?php
// app/Filters/ApiAuthFilter.php

namespace App\Filters;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;

class ApiAuthFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        $token = $request->getHeaderLine('Authorization');

        if (empty($token)) {
            return service('response')
                ->setJSON(['success' => false, 'message' => 'Authorization token required'])
                ->setStatusCode(401);
        }

        // Validate token logic here
        if (!$this->validateToken($token)) {
            return service('response')
                ->setJSON(['success' => false, 'message' => 'Invalid or expired token'])
                ->setStatusCode(401);
        }
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Add CORS headers for API responses
        $response->setHeader('Access-Control-Allow-Origin', '*');
        $response->setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');
        $response->setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    }

    private function validateToken($token)
    {
        // Token validation logic
        return true; // Placeholder
    }
}
```

## 7. User Workflows

### 6.1 Buyer Workflow (Planned)
1. **Login** → Session-based authentication
2. **View Dashboard** → Display assigned commodities and recent transactions
3. **Select Commodity** → Choose from assigned commodity list
4. **Select Customer** → Choose existing or create new customer
5. **Enter Transaction Details** → Quantity, price, payment method
6. **Submit Transaction** → Server-side validation and database storage
7. **Generate Receipt** → PDF receipt generation and download

### 6.2 Admin Workflow
1. **Login** → Session-based authentication with admin privileges
2. **User Management** → Create, edit, and manage user accounts
3. **Role Assignment** → Assign buyer, supervisor, admin roles
4. **System Configuration** → Manage application settings
5. **Reports & Analytics** → View system-wide reports and statistics

### 6.3 Supervisor Workflow (Planned)
1. **Login** → Session-based authentication with supervisor privileges
2. **Transaction Monitoring** → View and approve buyer transactions
3. **Performance Reports** → Analyze buyer and commodity performance
### 6.4 Evaluator Workflow (Planned)
1. **Login** → Session-based authentication with evaluator privileges
2. **Quality Assessment** → Record commodity quality and grading
3. **Price Validation** → Verify and approve transaction prices
4. **Audit Reports** → Generate quality and compliance audit trails

## 7. Security Considerations

### 7.1 Authentication Security
- **Password hashing** with PHP password_hash() (bcrypt/argon2)
- **Session management** with CodeIgniter 4 session library
- **Session regeneration** on login to prevent session fixation
- **Account status validation** (active/inactive/suspended)
- **Login attempt monitoring** and rate limiting

### 7.2 Data Security
- **HTTPS enforcement** for all production traffic
- **Database encryption** at rest with Supabase
- **Input validation** with CodeIgniter 4 validation library
- **SQL injection prevention** with query builder and prepared statements
- **XSS protection** with output escaping and CSP headers

### 7.3 Access Control
- **Role-based permissions** with database-driven roles
- **Route-level authorization** with CodeIgniter 4 filters
- **CSRF protection** on all form submissions
- **Session timeout** and automatic logout
- **Audit logging** for sensitive operations

## 8. Performance Considerations

### 8.1 Database Optimization
- **Proper indexing strategy** for frequently queried columns
- **Query optimization** with CodeIgniter 4 query builder
- **Connection pooling** with Supabase PostgreSQL
- **Pagination** for large result sets
- **Database monitoring** with Supabase dashboard

### 8.2 Application Performance
- **CodeIgniter 4 caching** for frequently accessed data
- **View caching** for static content
- **Gzip compression** for HTTP responses
- **Asset minification** for CSS/JavaScript files
- **Image optimization** for uploaded files

### 8.3 Web Performance
- **Responsive design** for mobile compatibility
- **Lazy loading** for images and content
- **Browser caching** with appropriate headers
- **CDN integration** for static assets (if needed)

## 9. Monitoring & Analytics

### 9.1 System Monitoring
- **Application logging** with CodeIgniter 4 logging library
- **Database monitoring** with Supabase dashboard and metrics
- **Error tracking** with custom error handlers
- **Performance monitoring** with server metrics
- **Security monitoring** for failed login attempts

### 9.2 Business Analytics (Planned)
- **Transaction reporting** with database queries
- **User activity tracking** with audit logs
- **Commodity performance metrics** with aggregated data
- **Revenue reporting** with financial calculations
- **Dashboard metrics** with real-time statistics

## 10. Deployment Architecture

### 10.1 Environment Strategy
- **Development** → XAMPP local environment with Supabase
- **Staging** → Shared hosting or VPS for testing
- **Production** → Web hosting with PHP 8.1+ and SSL

### 10.2 Infrastructure Requirements
- **Web Server** → Apache or Nginx with PHP 8.1+
- **Database** → Supabase PostgreSQL (cloud-hosted)
- **SSL Certificate** → HTTPS encryption for security
- **File Storage** → Server file system or cloud storage
- **Backup Strategy** → Database backups and file backups

## 11. File Management Implementation (Planned)

### 11.1 File Storage Structure
```
dcbuyer-files/
├── uploads/
│   ├── profiles/
│   │   └── {userId}/
│   │       └── avatar.jpg
│   ├── transactions/
│   │   └── {transactionId}/
│   │       ├── receipts/
│   │       └── photos/
│   ├── commodities/
│   │   └── {commodityId}/
│   │       └── images/
│   └── customers/
│       └── {customerId}/
│           └── documents/
├── templates/
│   ├── receipt_template.pdf
│   └── report_templates/
└── exports/
    ├── reports/
    └── backups/
```

### 11.2 File Upload Service (CodeIgniter 4)
```php
<?php
class FileUploadService
{
    protected $uploadPath = WRITEPATH . 'uploads/';

    public function uploadFile($file, $entityType, $entityId)
    {
        $validation = \Config\Services::validation();
        $validation->setRules([
            'file' => 'uploaded[file]|max_size[file,2048]|ext_in[file,jpg,jpeg,png,pdf]'
        ]);

        if (!$validation->withRequest($this->request)->run()) {
            return false;
        }

        $newName = $file->getRandomName();
        $path = $entityType . '/' . $entityId . '/';

        if ($file->move($this->uploadPath . $path, $newName)) {
            return $path . $newName;
        }

        return false;
    }
}
```

### 11.3 File Security and Access Control
- **Upload validation** with file type and size restrictions
- **Secure file paths** outside web root directory
- **Access control** with authentication checks
- **File scanning** for malware (if implemented)
- **Backup strategy** for uploaded files

## 12. Future Enhancements

### 12.1 Planned Features
- **Customer Management Module** - Complete CRUD operations for customers
- **Commodity Management Module** - Catalog management with pricing
- **Transaction Processing Module** - Full transaction workflow
- **Assignment Management Module** - Buyer-commodity assignments
- **Reporting Dashboard** - Analytics and business intelligence
- **Mobile-Responsive Interface** - Enhanced mobile experience
- **Email Notifications** - Transaction and system notifications
- **PDF Report Generation** - Automated report creation
- **Data Export/Import** - Excel/CSV data handling
- **Advanced Search** - Full-text search capabilities

### 12.2 Technical Enhancements
- **API Development** - RESTful API for mobile app integration
- **Caching Implementation** - Redis or Memcached for performance
- **Queue System** - Background job processing
- **Multi-language Support** - Internationalization (i18n)
- **Advanced Security** - Two-factor authentication, audit trails
- **Performance Optimization** - Database query optimization
- **Automated Testing** - Unit and integration tests
- **CI/CD Pipeline** - Automated deployment process

### 12.3 Scalability Considerations
- **Load Balancing** - Multiple server instances
- **Database Optimization** - Read replicas and partitioning
- **CDN Integration** - Content delivery network for assets
- **Microservices Migration** - Service-oriented architecture (future)

---

*This document serves as the foundation for the DCBuyer web application development using CodeIgniter 4 and Supabase PostgreSQL. It should be updated as requirements evolve and new features are implemented.*
