<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateCountriesTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'SERIAL',
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'code' => [
                'type' => 'VARCHAR',
                'constraint' => 3,
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('code');
        $this->forge->createTable('countries');

        // Set default timestamp
        $this->db->query('ALTER TABLE countries ALTER COLUMN created_at SET DEFAULT CURRENT_TIMESTAMP');

        // Add indexes for performance
        $this->db->query('CREATE INDEX idx_countries_name ON countries(name)');
        $this->db->query('CREATE INDEX idx_countries_code ON countries(code)');
    }

    public function down()
    {
        $this->forge->dropTable('countries');
    }
}
