<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .detail-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .detail-section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }
    
    .detail-row {
        display: flex;
        margin-bottom: 1rem;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .detail-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .detail-label {
        font-weight: 600;
        color: #495057;
        min-width: 150px;
        flex-shrink: 0;
    }
    
    .detail-value {
        color: #6c757d;
        flex-grow: 1;
    }
    
    .hierarchy-path {
        background-color: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .hierarchy-path h6 {
        color: #1976d2;
        margin-bottom: 0.5rem;
    }
    
    .hierarchy-breadcrumb {
        font-size: 1rem;
        color: #424242;
        font-weight: 500;
    }
    
    .gps-coordinates {
        font-family: monospace;
        background-color: #f8f9fa;
        padding: 0.5rem;
        border-radius: 6px;
        border: 1px solid #dee2e6;
    }
    
    .gps-map-link {
        margin-top: 0.5rem;
    }
    
    .badge-status {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .badge-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .audit-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .audit-info .audit-item {
        margin-bottom: 0.5rem;
    }
    
    .audit-info .audit-item:last-child {
        margin-bottom: 0;
    }
    
    .empty-value {
        color: #adb5bd;
        font-style: italic;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Display Success Message -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Location Hierarchy -->
    <div class="detail-section">
        <div class="detail-section-title">
            <i class="fas fa-sitemap me-2"></i>Location Hierarchy
        </div>
        
        <div class="hierarchy-path">
            <h6><i class="fas fa-map-marked-alt me-2"></i>Location Path</h6>
            <div class="hierarchy-breadcrumb">
                <?= esc($location['country_name']) ?> → 
                <?= esc($location['province_name']) ?> → 
                <?= esc($location['district_name']) ?> → 
                <?= esc($location['llg_name']) ?> → 
                <strong><?= esc($location['ward']) ?></strong>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-3">
                <div class="detail-row">
                    <div class="detail-label">Country:</div>
                    <div class="detail-value">
                        <strong><?= esc($location['country_name']) ?></strong>
                        <small class="text-muted">(<?= esc($location['country_code']) ?>)</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="detail-row">
                    <div class="detail-label">Province:</div>
                    <div class="detail-value"><?= esc($location['province_name']) ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="detail-row">
                    <div class="detail-label">District:</div>
                    <div class="detail-value"><?= esc($location['district_name']) ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="detail-row">
                    <div class="detail-label">LLG:</div>
                    <div class="detail-value"><?= esc($location['llg_name']) ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Location Details -->
    <div class="detail-section">
        <div class="detail-section-title">
            <i class="fas fa-map-marker-alt me-2"></i>Location Details
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="detail-row">
                    <div class="detail-label">Location Name:</div>
                    <div class="detail-value">
                        <strong><?= esc($location['location_name']) ?></strong>
                        <span class="badge badge-status badge-active ms-2">Active</span>
                    </div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">Ward:</div>
                    <div class="detail-value"><?= esc($location['ward']) ?></div>
                </div>
                
                <div class="detail-row">
                    <div class="detail-label">Remarks:</div>
                    <div class="detail-value">
                        <?php if (!empty($location['remarks'])): ?>
                            <?= nl2br(esc($location['remarks'])) ?>
                        <?php else: ?>
                            <span class="empty-value">No remarks provided</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="detail-row">
                    <div class="detail-label">GPS Coordinates:</div>
                    <div class="detail-value">
                        <?php if (!empty($location['gps_latitude']) && !empty($location['gps_longitude'])): ?>
                            <div class="gps-coordinates">
                                <strong>Latitude:</strong> <?= number_format($location['gps_latitude'], 6) ?><br>
                                <strong>Longitude:</strong> <?= number_format($location['gps_longitude'], 6) ?>
                            </div>
                            <div class="gps-map-link">
                                <a href="https://www.google.com/maps?q=<?= $location['gps_latitude'] ?>,<?= $location['gps_longitude'] ?>" 
                                   target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-map me-2"></i>View on Google Maps
                                </a>
                            </div>
                        <?php else: ?>
                            <span class="empty-value">GPS coordinates not available</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Audit Information -->
    <div class="detail-section">
        <div class="detail-section-title">
            <i class="fas fa-history me-2"></i>Audit Information
        </div>
        
        <div class="audit-info">
            <div class="row">
                <div class="col-md-6">
                    <div class="audit-item">
                        <strong>Created By:</strong> <?= esc($location['created_by_name']) ?>
                    </div>
                    <div class="audit-item">
                        <strong>Created Date:</strong> <?= date('F j, Y \a\t g:i A', strtotime($location['created_at'])) ?>
                    </div>
                </div>
                <div class="col-md-6">
                    <?php if (!empty($location['updated_by_name'])): ?>
                        <div class="audit-item">
                            <strong>Last Updated By:</strong> <?= esc($location['updated_by_name']) ?>
                        </div>
                        <div class="audit-item">
                            <strong>Last Updated:</strong> <?= date('F j, Y \a\t g:i A', strtotime($location['updated_at'])) ?>
                        </div>
                    <?php else: ?>
                        <div class="audit-item">
                            <strong>Last Updated:</strong> <span class="empty-value">Never updated</span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="detail-section">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <a href="<?= base_url('admin/locations') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Locations
                </a>
            </div>
            <div>
                <a href="<?= base_url('admin/locations/' . $location['id'] . '/edit') ?>" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>Edit Location
                </a>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                    <i class="fas fa-trash me-2"></i>Delete Location
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the location "<strong><?= esc($location['location_name']) ?></strong>"?</p>
                <p class="text-danger"><small>This action cannot be undone and may affect related records.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="<?= base_url('admin/locations/' . $location['id']) ?>" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete Location</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function confirmDelete() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Auto-hide success messages after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const successAlert = document.querySelector('.alert-success');
    if (successAlert) {
        setTimeout(function() {
            const alert = new bootstrap.Alert(successAlert);
            alert.close();
        }, 5000);
    }
});
</script>
<?= $this->endSection() ?>
