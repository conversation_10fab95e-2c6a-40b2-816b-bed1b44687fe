<?php

namespace App\Models;

use CodeIgniter\Model;

class ProvinceModel extends Model
{
    protected $table = 'provinces';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'country_id',
        'name',
        'code'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = '';

    // Validation
    protected $validationRules = [
        'country_id' => 'required|integer|is_not_unique[countries.id]',
        'name' => 'required|min_length[2]|max_length[100]',
        'code' => 'permit_empty|max_length[10]'
    ];

    protected $validationMessages = [
        'country_id' => [
            'required' => 'Country is required.',
            'integer' => 'Country must be a valid selection.',
            'is_not_unique' => 'Selected country does not exist.'
        ],
        'name' => [
            'required' => 'Province name is required.',
            'min_length' => 'Province name must be at least 2 characters long.',
            'max_length' => 'Province name cannot exceed 100 characters.'
        ],
        'code' => [
            'max_length' => 'Province code cannot exceed 10 characters.'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get provinces by country for dropdown
     */
    public function getByCountryForDropdown(int $countryId): array
    {
        return $this->select('id, name')
                    ->where('country_id', $countryId)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get all provinces for dropdown
     */
    public function getForDropdown(): array
    {
        return $this->select('provinces.id, provinces.name, countries.name as country_name')
                    ->join('countries', 'countries.id = provinces.country_id')
                    ->orderBy('countries.name, provinces.name', 'ASC')
                    ->findAll();
    }

    /**
     * Get provinces with country information
     */
    public function getProvincesWithCountry(int $perPage = 20, string $search = '', int $countryId = null): array
    {
        $builder = $this->select('provinces.*, countries.name as country_name')
                        ->join('countries', 'countries.id = provinces.country_id');
        
        if (!empty($search)) {
            $builder->groupStart()
                    ->like('provinces.name', $search)
                    ->orLike('provinces.code', $search)
                    ->orLike('countries.name', $search)
                    ->groupEnd();
        }
        
        if ($countryId) {
            $builder->where('provinces.country_id', $countryId);
        }
        
        return $builder->orderBy('countries.name, provinces.name', 'ASC')
                      ->paginate($perPage);
    }

    /**
     * Get province with country details
     */
    public function getProvinceWithCountry(int $id): ?array
    {
        return $this->select('provinces.*, countries.name as country_name, countries.code as country_code')
                    ->join('countries', 'countries.id = provinces.country_id')
                    ->where('provinces.id', $id)
                    ->first();
    }

    /**
     * Create a new province
     */
    public function createProvince(array $data): bool
    {
        return $this->insert($data) !== false;
    }

    /**
     * Update province data
     */
    public function updateProvince(int $id, array $data): bool
    {
        return $this->update($id, $data);
    }

    /**
     * Check if province has districts
     */
    public function hasDistricts(int $provinceId): bool
    {
        $districtModel = new \App\Models\DistrictModel();
        return $districtModel->where('province_id', $provinceId)->countAllResults() > 0;
    }

    /**
     * Get province statistics
     */
    public function getProvinceStats(): array
    {
        return [
            'total_provinces' => $this->countAllResults(),
            'recent_provinces' => $this->where('created_at >=', date('Y-m-d H:i:s', strtotime('-30 days')))
                                      ->countAllResults()
        ];
    }
}
