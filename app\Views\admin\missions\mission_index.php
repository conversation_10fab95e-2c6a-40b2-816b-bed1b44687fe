<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Mission Statistics -->
<div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-tasks"></i>
            </div>
            <div class="stat-number"><?= $stats['total'] ?></div>
            <p class="stat-label">Total Missions</p>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-number"><?= $stats['pending'] ?></div>
            <p class="stat-label">Pending</p>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-play"></i>
            </div>
            <div class="stat-number"><?= $stats['in_progress'] ?></div>
            <p class="stat-label">In progress</p>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-check"></i>
            </div>
            <div class="stat-number"><?= $stats['completed'] ?></div>
            <p class="stat-label">Completed</p>
        </div>
</div>

<!-- Missions Table -->
<div class="table-section">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="search-container">
            <label for="search">Search:</label>
            <input type="text" id="search" class="form-control" placeholder="Search missions..." value="<?= esc($search) ?>">
        </div>
        <button id="exportBtn" class="btn btn-admin-secondary">
            <i class="fas fa-download me-2"></i>Export to Excel
        </button>
    </div>

    <div class="table-responsive">
                <table id="missionsTable" class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>Mission #</th>
                            <th>Mission Name</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Buyer</th>
                            <th>Commodity</th>
                            <th>Location</th>
                            <th>Budget</th>
                            <th>Actual</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($missions)): ?>
                        <tr>
                            <td colspan="10" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-tasks fa-3x mb-3"></i>
                                    <p class="mb-0">No missions found.</p>
                                    <small>Create your first mission to get started.</small>
                                </div>
                            </td>
                        </tr>
                        <?php else: ?>
                            <?php foreach ($missions as $mission): ?>
                            <tr>
                                <td>
                                    <span class="fw-bold text-primary"><?= esc($mission['mission_number']) ?></span>
                                </td>
                                <td>
                                    <div class="fw-bold"><?= esc($mission['mission_name']) ?></div>
                                </td>
                                <td>
                                    <span class="text-muted">
                                        <?= date('M j, Y', strtotime($mission['mission_date'])) ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="status-badge status-<?= $mission['mission_status'] ?>">
                                        <?= ucfirst(str_replace('_', ' ', $mission['mission_status'])) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if (!empty($mission['user_name'])): ?>
                                        <div class="fw-bold"><?= esc($mission['user_name']) ?></div>
                                        <small class="text-muted"><?= esc($mission['username']) ?></small>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($mission['commodity_name'])): ?>
                                        <div class="fw-bold"><?= esc($mission['commodity_name']) ?></div>
                                        <small class="text-muted"><?= esc($mission['unit_of_measurement']) ?></small>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($mission['location_name'])): ?>
                                        <span class="fw-bold text-primary">
                                            <i class="fas fa-map-marker-alt me-1"></i><?= esc($mission['location_name']) ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($mission['budgeted_amount'])): ?>
                                        $<?= number_format($mission['budgeted_amount'], 2) ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($mission['actual_amount'])): ?>
                                        $<?= number_format($mission['actual_amount'], 2) ?>
                                        <?php if (!empty($mission['budgeted_amount'])): ?>
                                            <?php
                                            $variance = $mission['actual_amount'] - $mission['budgeted_amount'];
                                            $varianceClass = $variance > 0 ? 'text-danger' : ($variance < 0 ? 'text-success' : 'text-muted');
                                            ?>
                                            <br><small class="<?= $varianceClass ?>">
                                                <?= $variance > 0 ? '+' : '' ?>$<?= number_format($variance, 2) ?>
                                            </small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="<?= base_url('admin/missions/' . $mission['id']) ?>" class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('admin/missions/' . $mission['id'] . '/edit') ?>" class="btn btn-sm btn-outline-secondary" title="Edit Mission">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" title="Delete Mission" onclick="deleteMission(<?= $mission['id'] ?>, '<?= esc($mission['mission_name']) ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
            </table>
        </div>

    <!-- Pagination -->
    <?php if (isset($pager) && $pager->getPageCount() > 1): ?>
    <div class="pagination-container">
        <?= $pager->links() ?>
    </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the mission "<span id="missionName"></span>"?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete Mission</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.table-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 2rem;
    color: #2E7D32;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2E7D32;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-weight: 500;
    margin: 0;
}

.search-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-container label {
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.search-container input {
    width: 300px;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: capitalize;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-in_progress {
    background-color: #cce5ff;
    color: #004085;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-buttons .btn {
    padding: 0.375rem 0.75rem;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<script>
// Enhanced table functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search');
    const table = document.getElementById('missionsTable');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    // Real-time search functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            
            rows.forEach(row => {
                if (row.cells.length === 1) return; // Skip "no data" row
                
                const text = row.textContent.toLowerCase();
                const isVisible = text.includes(searchTerm);
                row.style.display = isVisible ? '' : 'none';
            });
            
            updatePagination();
        });
    }
    
    // Client-side pagination
    const rowsPerPage = 25;
    let currentPage = 1;
    
    function updatePagination() {
        const visibleRows = rows.filter(row => row.style.display !== 'none' && row.cells.length > 1);
        const totalPages = Math.ceil(visibleRows.length / rowsPerPage);
        
        visibleRows.forEach((row, index) => {
            const pageNumber = Math.floor(index / rowsPerPage) + 1;
            row.style.display = pageNumber === currentPage ? '' : 'none';
        });
        
        // Update pagination controls (if needed)
        updatePaginationControls(totalPages);
    }
    
    function updatePaginationControls(totalPages) {
        // Implementation for pagination controls
        // This would create page number buttons
    }
    
    // Excel export functionality
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportToExcel();
        });
    }
});

function exportToExcel() {
    const table = document.getElementById('missionsTable');
    const rows = table.querySelectorAll('tr');
    const data = [];

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll(index === 0 ? 'th' : 'td');
        const rowData = [];

        cells.forEach((cell, cellIndex) => {
            // Skip the Actions column (last column)
            if (cellIndex < cells.length - 1) {
                let cellText = cell.textContent.trim();
                // Clean up status badges
                if (cell.querySelector('.badge')) {
                    cellText = cell.querySelector('.badge').textContent.trim();
                }
                // Clean up currency formatting for numbers
                if (cellText.startsWith('PGK')) {
                    const numValue = parseFloat(cellText.replace(/[PGK,]/g, ''));
                    rowData.push(isNaN(numValue) ? cellText : numValue);
                } else {
                    rowData.push(cellText);
                }
            }
        });

        if (rowData.length > 0) {
            data.push(rowData);
        }
    });

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(data);

    // Set column widths
    const colWidths = [
        { wch: 12 }, // Mission #
        { wch: 35 }, // Mission Name
        { wch: 12 }, // Date
        { wch: 12 }, // Status
        { wch: 20 }, // Buyer
        { wch: 20 }, // Commodity
        { wch: 25 }, // Location
        { wch: 12 }, // Budget
        { wch: 12 }  // Actual
    ];
    ws['!cols'] = colWidths;

    // Style the header row
    const headerRange = XLSX.utils.decode_range(ws['!ref']);
    for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!ws[cellAddress]) continue;
        ws[cellAddress].s = {
            font: { bold: true },
            fill: { fgColor: { rgb: "EEEEEE" } }
        };
    }

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, "Missions");

    // Generate filename with current date
    const now = new Date();
    const filename = `missions_export_${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}.xlsx`;

    // Save file
    XLSX.writeFile(wb, filename);
}

function deleteMission(id, name) {
    document.getElementById('missionName').textContent = name;
    document.getElementById('deleteForm').action = `<?= base_url('admin/missions/') ?>${id}/delete`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
<?= $this->endSection() ?>
