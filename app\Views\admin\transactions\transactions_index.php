<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<!-- SheetJS for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<style>
    /* Custom Table Controls Styling */
    .table-controls {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-controls input[type="text"] {
        border-radius: 8px;
        border: 1px solid #ddd;
        padding: 0.5rem;
    }

    .table-controls input[type="text"]:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    }

    .table-pagination {
        background: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .table-info {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .pagination .page-link {
        border-radius: 8px;
        margin: 0 2px;
        border: 1px solid #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: var(--bs-primary);
        border-color: var(--bs-primary);
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= number_format($stats['total_transactions']) ?></h4>
                        <p class="mb-0">Total Transactions</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-receipt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= number_format($stats['pending_transactions']) ?></h4>
                        <p class="mb-0">Pending</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= number_format($stats['completed_transactions']) ?></h4>
                        <p class="mb-0">Completed</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">PGK <?= number_format($stats['total_amount'], 2) ?></h4>
                        <p class="mb-0">Total Amount</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-coins fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">Filter Transactions</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?= base_url('admin/transactions') ?>">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="search">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="<?= $filters['search'] ?? '' ?>" 
                               placeholder="Transaction code, user, mission...">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select class="form-control" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="pending" <?= ($filters['status'] ?? '') === 'pending' ? 'selected' : '' ?>>Pending</option>
                            <option value="completed" <?= ($filters['status'] ?? '') === 'completed' ? 'selected' : '' ?>>Completed</option>
                            <option value="cancelled" <?= ($filters['status'] ?? '') === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="user_id">Buyer</label>
                        <select class="form-control" id="user_id" name="user_id">
                            <option value="">All Buyers</option>
                            <?php foreach ($users as $user): ?>
                                <option value="<?= $user['id'] ?>" <?= ($filters['user_id'] ?? '') == $user['id'] ? 'selected' : '' ?>>
                                    <?= esc($user['fullname']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="date_from">Date From</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" 
                               value="<?= $filters['date_from'] ?? '' ?>">
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label for="date_to">Date To</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" 
                               value="<?= $filters['date_to'] ?? '' ?>">
                    </div>
                </div>
                <div class="col-md-1">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary btn-block">Filter</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Transactions Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Transactions List</h5>
    </div>
    <div class="card-body">
        <?php if (empty($transactions)): ?>
            <div class="text-center py-4">
                <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No transactions found</h5>
                <p class="text-muted">Start by creating your first transaction.</p>
                <a href="<?= base_url('admin/transactions/create') ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Transaction
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table id="transactionsTable" class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>Transaction Code</th>
                            <th>Date</th>
                            <th>Buyer</th>
                            <th>Mission</th>
                            <th>Commodity</th>
                            <th>Customer</th>
                            <th>Quantity</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($transactions as $transaction): ?>
                            <tr>
                                <td>
                                    <strong><?= esc($transaction['transaction_code']) ?></strong>
                                </td>
                                <td><?= date('M d, Y', strtotime($transaction['transaction_date'])) ?></td>
                                <td><?= esc($transaction['user_name']) ?></td>
                                <td>
                                    <small class="text-muted"><?= esc($transaction['mission_number']) ?></small><br>
                                    <?= esc($transaction['mission_name']) ?>
                                </td>
                                <td>
                                    <?= esc($transaction['commodity_name']) ?><br>
                                    <small class="text-muted"><?= esc($transaction['unit_of_measurement']) ?></small>
                                </td>
                                <td>
                                    <?php if ($transaction['customer_first_name']): ?>
                                        <?= esc($transaction['customer_first_name'] . ' ' . $transaction['customer_last_name']) ?><br>
                                        <small class="text-muted"><?= esc($transaction['customer_code']) ?></small>
                                    <?php else: ?>
                                        <span class="text-muted">No customer</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= number_format($transaction['quantity'], 3) ?></td>
                                <td>PGK <?= number_format($transaction['payment_amount'], 2) ?></td>
                                <td>
                                    <?= ucfirst($transaction['status']) ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?= base_url('admin/transactions/' . $transaction['id']) ?>" 
                                           class="btn btn-outline-info" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= base_url('admin/transactions/' . $transaction['id'] . '/edit') ?>" 
                                           class="btn btn-outline-primary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteTransaction(<?= $transaction['id'] ?>)" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($pager): ?>
                <div class="d-flex justify-content-center mt-3">
                    <?= $pager->links() ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this transaction? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
function deleteTransaction(id) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `<?= base_url('admin/transactions') ?>/${id}`;

    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Enhanced Table Implementation
function initializeEnhancedTable(tableId, options = {}) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const config = {
        searchPlaceholder: options.searchPlaceholder || 'Search...',
        exportFilename: options.exportFilename || 'table_export',
        rowsPerPage: options.rowsPerPage || 25,
        ...options
    };

    // Add search functionality
    addTableSearch(tableId, config);

    // Add export functionality
    addExportButton(tableId, config);

    // Add pagination
    addTablePagination(tableId, config);
}

function addTableSearch(tableId, config) {
    const table = document.getElementById(tableId);
    const tableContainer = table.closest('.table-responsive').parentNode;

    // Create search container
    const searchContainer = document.createElement('div');
    searchContainer.className = 'table-controls mb-3 d-flex justify-content-between align-items-center';

    // Create search input
    const searchDiv = document.createElement('div');
    searchDiv.className = 'd-flex align-items-center';
    searchDiv.innerHTML = `
        <label class="me-2 mb-0">Search:</label>
        <input type="text" id="${tableId}Search" class="form-control" style="width: 300px;" placeholder="${config.searchPlaceholder}">
    `;

    // Create export button
    const exportDiv = document.createElement('div');
    exportDiv.innerHTML = `
        <button type="button" id="${tableId}ExportExcel" class="btn btn-success">
            <i class="fas fa-file-excel me-2"></i>Export to Excel
        </button>
    `;

    searchContainer.appendChild(searchDiv);
    searchContainer.appendChild(exportDiv);
    tableContainer.insertBefore(searchContainer, tableContainer.firstChild);

    // Add search functionality
    const searchInput = document.getElementById(`${tableId}Search`);
    searchInput.addEventListener('input', function() {
        filterTable(tableId, this.value.toLowerCase());
    });
}

function filterTable(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    updateTableInfo(tableId);
}

function addExportButton(tableId, config) {
    const exportBtn = document.getElementById(`${tableId}ExportExcel`);
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportTableToExcel(tableId, config.exportFilename);
        });
    }
}

function exportTableToExcel(tableId, filename) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tr');
    const data = [];

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll(index === 0 ? 'th' : 'td');
        const rowData = [];

        cells.forEach((cell, cellIndex) => {
            // Skip the Actions column (last column)
            if (cellIndex < cells.length - 1) {
                let cellText = cell.textContent.trim();
                // Clean up status badges
                if (cell.querySelector('.badge')) {
                    cellText = cell.querySelector('.badge').textContent.trim();
                }
                // Clean up currency formatting for numbers
                if (cellText.startsWith('PGK')) {
                    const numValue = parseFloat(cellText.replace(/[PGK,]/g, ''));
                    rowData.push(isNaN(numValue) ? cellText : numValue);
                } else {
                    rowData.push(cellText);
                }
            }
        });

        if (rowData.length > 0) {
            data.push(rowData);
        }
    });

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(data);

    // Set column widths (adjust as needed for your table)
    const colWidths = [
        { wch: 20 }, // Transaction Code
        { wch: 12 }, // Date
        { wch: 15 }, // Buyer
        { wch: 30 }, // Mission
        { wch: 15 }, // Commodity
        { wch: 20 }, // Customer
        { wch: 10 }, // Quantity
        { wch: 12 }, // Amount
        { wch: 12 }  // Status
    ];
    ws['!cols'] = colWidths;

    // Style the header row
    const headerRange = XLSX.utils.decode_range(ws['!ref']);
    for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!ws[cellAddress]) continue;
        ws[cellAddress].s = {
            font: { bold: true, color: { rgb: "FFFFFF" } },
            fill: { fgColor: { rgb: "2E7D32" } },
            alignment: { horizontal: "center" }
        };
    }

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(wb, ws, "Transactions Export");

    // Generate filename with current date
    const exportFilename = filename + '_' + new Date().toISOString().split('T')[0] + '.xlsx';

    // Save the file
    XLSX.writeFile(wb, exportFilename);

    // Show success notification (if AdminTemplate.showNotification exists)
    if (typeof AdminTemplate !== 'undefined' && AdminTemplate.showNotification) {
        AdminTemplate.showNotification('Excel file exported successfully!', 'success');
    } else {
        alert('Excel file exported successfully!');
    }
}

function addTablePagination(tableId, config) {
    const table = document.getElementById(tableId);
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    if (rows.length <= config.rowsPerPage) return; // No pagination needed for small tables

    let currentPage = 1;
    const rowsPerPage = config.rowsPerPage;
    const totalPages = Math.ceil(rows.length / rowsPerPage);

    // Create pagination container
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'table-pagination mt-3 d-flex justify-content-between align-items-center';
    paginationContainer.innerHTML = `
        <div class="table-info">
            <span id="${tableId}TableInfo">Showing 1 to ${Math.min(rowsPerPage, rows.length)} of ${rows.length} entries</span>
        </div>
        <nav>
            <ul class="pagination mb-0" id="${tableId}TablePagination">
                <!-- Pagination buttons will be generated here -->
            </ul>
        </nav>
    `;

    table.parentNode.appendChild(paginationContainer);

    function showPage(page) {
        const start = (page - 1) * rowsPerPage;
        const end = start + rowsPerPage;

        rows.forEach((row, index) => {
            if (index >= start && index < end && row.style.display !== 'none') {
                row.style.display = '';
            } else if (row.style.display !== 'none') {
                row.style.display = 'none';
            }
        });

        updatePagination(tableId, page, totalPages, showPage);
        updateTableInfo(tableId);
    }

    // Initialize first page
    showPage(1);
}

function updatePagination(tableId, currentPage, totalPages, showPageCallback) {
    const pagination = document.getElementById(`${tableId}TablePagination`);
    pagination.innerHTML = '';

    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>`;
    pagination.appendChild(prevLi);

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
        pagination.appendChild(li);
    }

    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>`;
    pagination.appendChild(nextLi);

    // Add click events
    pagination.addEventListener('click', function(e) {
        e.preventDefault();
        if (e.target.classList.contains('page-link') && !e.target.parentNode.classList.contains('disabled')) {
            const page = parseInt(e.target.dataset.page);
            if (page >= 1 && page <= totalPages) {
                showPageCallback(page);
            }
        }
    });
}

function updateTableInfo(tableId) {
    const tableInfo = document.getElementById(`${tableId}TableInfo`);
    if (!tableInfo) return;

    const table = document.getElementById(tableId);
    const visibleRows = table.querySelectorAll('tbody tr[style=""], tbody tr:not([style])');
    const totalRows = table.querySelectorAll('tbody tr').length;

    tableInfo.textContent = `Showing ${visibleRows.length} of ${totalRows} entries`;
}

// Initialize the enhanced table when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeEnhancedTable('transactionsTable', {
        searchPlaceholder: 'Search transactions by code, buyer, mission, commodity...',
        exportFilename: 'transactions_export',
        rowsPerPage: 25
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
