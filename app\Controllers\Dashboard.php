<?php

namespace App\Controllers;

class Dashboard extends BaseController
{
    public function index()
    {
        // Get user data from session
        $data = [
            'title' => 'Dashboard - DCBuyer',
            'active_menu' => 'dashboard',
            'user_email' => session()->get('email'),
            'user_name' => session()->get('fullname') ?: session()->get('username') ?: 'User',
            'user_role' => session()->get('is_admin') ? 'administrator' : (session()->get('is_supervisor') ? 'supervisor' : 'user')
        ];

        return view('dashboard', $data);
    }
}
