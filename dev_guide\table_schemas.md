CREATE TABLE commodities (
    commodity_id   SERIAL PRIMARY KEY,
    commodity_name VARCHAR(255) NOT NULL,
    unit_of_measurement ENUM('kg', 'g', 'lb', 'ton', 'liter', 'ml', 'gallon', 'piece', 'unit', 'box', 'carton', 'pack') NOT NULL,
    remarks        TEXT,

    -- Audit fields
    created_at     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by     INT NOT NULL,
    updated_at     TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by     INT NOT NULL,

    -- Soft delete fields
    deleted_at     TIMESTAMP,
    deleted_by     INT,
    is_deleted     BOOLEAN NOT NULL DEFAULT FALSE
);




CREATE TABLE buy_transaction (
    -- Primary key
    id BIGINT AUTO_INCREMENT PRIMARY KEY,

    -- Core fields
    user_id INT NOT NULL,
    commodity_id INT NOT NULL,
    customer_id INT NULL,
    location_id INT NOT NULL,
    quantity DECIMAL(15,3) NOT NULL,  -- Supports decimal quantities like 1.5 kg, 2.75 liters
    amount DECIMAL(15,2) NOT NULL,    -- Monetary amount with 2 decimal places
    remarks TEXT NULL,

    -- Audit fields
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT NOT NULL,

    -- Soft delete fields
    deleted_at TIMESTAMP NULL,
    deleted_by INT NULL,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,

    -- Indexes for better performance
    INDEX idx_user_id (user_id),
    INDEX idx_commodity_id (commodity_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_location_id (location_id),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_user_commodity (user_id, commodity_id)
);

-- Optional: Add a comment to describe the table
ALTER TABLE buy_transaction COMMENT = 'Stores buy transactions with user, commodity, and customer relationships';


CREATE TABLE locations (
    -- Primary key
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    
    -- Core fields
    country_id INT NOT NULL,
    province_id INT NOT NULL,
    district_id INT NOT NULL,
    llg_id INT NOT NULL,
    ward_id INT NOT NULL,
    location_name VARCHAR(255) NOT NULL,
    gps_coordinates POINT NULL,  -- For storing latitude/longitude as spatial data
    remarks TEXT NULL,            -- Additional notes or comments about the location
    
    -- Alternative GPS fields if you prefer separate columns
    -- latitude DECIMAL(10,8) NULL,
    -- longitude DECIMAL(11,8) NULL,
    
    -- Audit fields
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT NOT NULL,
    
    -- Soft delete fields
    deleted_at TIMESTAMP NULL,
    deleted_by INT NULL,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Indexes for better performance
    INDEX idx_country_id (country_id),
    INDEX idx_province_id (province_id),
    INDEX idx_district_id (district_id),
    INDEX idx_llg_id (llg_id),
    INDEX idx_ward_id (ward_id),
    INDEX idx_location_name (location_name),
    INDEX idx_created_at (created_at),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_hierarchy (country_id, province_id, district_id, llg_id, ward_id)
);

-- Optional: Add spatial index for GPS coordinates if using POINT type
-- CREATE SPATIAL INDEX idx_gps_coordinates ON locations(gps_coordinates);

-- Optional: Add a comment to describe the table
ALTER TABLE locations COMMENT = 'Stores hierarchical location data with GPS coordinates, remarks, and audit fields';

