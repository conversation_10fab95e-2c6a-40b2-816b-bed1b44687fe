<?php

namespace App\Models;

use CodeIgniter\Model;

class CustomerModel extends Model
{
    protected $table = 'customers';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'customer_code',
        'first_name',
        'last_name',
        'phone',
        'email',
        'address',
        'location_id',
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'customer_code' => 'permit_empty|max_length[20]|is_unique[customers.customer_code,id,{id}]',
        'first_name' => 'required|max_length[50]',
        'last_name' => 'required|max_length[50]',
        'phone' => 'permit_empty|max_length[20]',
        'email' => 'permit_empty|valid_email|max_length[100]|is_unique[customers.email,id,{id}]',
        'address' => 'permit_empty',
        'location_id' => 'permit_empty|integer',
        'status' => 'required|in_list[active,inactive]',
        'created_by' => 'required|integer'
    ];

    protected $validationMessages = [
        'customer_code' => [
            'is_unique' => 'Customer code must be unique.',
            'max_length' => 'Customer code cannot exceed 20 characters.'
        ],
        'first_name' => [
            'required' => 'First name is required.',
            'max_length' => 'First name cannot exceed 50 characters.'
        ],
        'last_name' => [
            'required' => 'Last name is required.',
            'max_length' => 'Last name cannot exceed 50 characters.'
        ],
        'phone' => [
            'max_length' => 'Phone number cannot exceed 20 characters.'
        ],
        'email' => [
            'valid_email' => 'Please enter a valid email address.',
            'max_length' => 'Email cannot exceed 100 characters.',
            'is_unique' => 'Email address is already registered.'
        ],
        'location_id' => [
            'integer' => 'Location must be a valid selection.'
        ],
        'status' => [
            'required' => 'Status is required.',
            'in_list' => 'Status must be active or inactive.'
        ],
        'created_by' => [
            'required' => 'Created by is required.',
            'integer' => 'Created by must be a valid user.'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['generateCustomerCode'];
    protected $beforeUpdate = [];
    protected $afterInsert = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Generate unique customer code before insert
     */
    protected function generateCustomerCode(array $data)
    {
        if (!isset($data['data']['customer_code']) || empty($data['data']['customer_code'])) {
            $data['data']['customer_code'] = $this->generateUniqueCustomerCode();
        }
        return $data;
    }

    /**
     * Generate unique customer code
     */
    public function generateUniqueCustomerCode(): string
    {
        $year = date('Y');
        
        // Get the last customer code for this year
        $lastCustomer = $this->select('customer_code')
            ->like('customer_code', "CUST{$year}", 'after')
            ->orderBy('id', 'DESC')
            ->first();

        if ($lastCustomer) {
            // Extract the increment number and add 1
            $lastCode = $lastCustomer['customer_code'];
            $increment = (int)substr($lastCode, -3) + 1;
        } else {
            $increment = 1;
        }

        return sprintf('CUST%s%03d', $year, $increment);
    }

    /**
     * Get customers with location details
     */
    public function getCustomersWithDetails(int $perPage = 20, array $filters = []): array
    {
        $builder = $this->select('
            customers.*,
            locations.location_name,
            locations.ward,
            countries.name as country_name,
            provinces.name as province_name,
            districts.name as district_name,
            llgs.name as llg_name
        ')
        ->join('locations', 'locations.id = customers.location_id', 'left')
        ->join('countries', 'countries.id = locations.country_id', 'left')
        ->join('provinces', 'provinces.id = locations.province_id', 'left')
        ->join('districts', 'districts.id = locations.district_id', 'left')
        ->join('llgs', 'llgs.id = locations.llg_id', 'left');

        // Apply filters
        if (!empty($filters['search'])) {
            $builder->groupStart()
                ->like('customers.customer_code', $filters['search'])
                ->orLike('customers.first_name', $filters['search'])
                ->orLike('customers.last_name', $filters['search'])
                ->orLike('customers.phone', $filters['search'])
                ->orLike('customers.email', $filters['search'])
                ->groupEnd();
        }

        if (!empty($filters['status'])) {
            $builder->where('customers.status', $filters['status']);
        }

        if (!empty($filters['location_id'])) {
            $builder->where('customers.location_id', $filters['location_id']);
        }

        return $builder->orderBy('customers.created_at', 'DESC')
            ->paginate($perPage);
    }

    /**
     * Get customer statistics
     */
    public function getCustomerStats(): array
    {
        $totalCustomers = $this->countAllResults();
        $activeCustomers = $this->where('status', 'active')->countAllResults();
        $inactiveCustomers = $this->where('status', 'inactive')->countAllResults();
        
        $recentCustomers = $this->where('created_at >=', date('Y-m-d H:i:s', strtotime('-30 days')))
            ->countAllResults();

        return [
            'total_customers' => $totalCustomers,
            'active_customers' => $activeCustomers,
            'inactive_customers' => $inactiveCustomers,
            'recent_customers' => $recentCustomers
        ];
    }

    /**
     * Get active customers for dropdown
     */
    public function getActiveCustomersForDropdown(): array
    {
        return $this->select('id, customer_code, first_name, last_name')
            ->where('status', 'active')
            ->orderBy('first_name', 'ASC')
            ->findAll();
    }

    /**
     * Find customer by code
     */
    public function findByCode(string $code)
    {
        return $this->where('customer_code', $code)->first();
    }

    /**
     * Find customer by email
     */
    public function findByEmail(string $email)
    {
        return $this->where('email', $email)->first();
    }

    /**
     * Find customer by phone
     */
    public function findByPhone(string $phone)
    {
        return $this->where('phone', $phone)->first();
    }

    /**
     * Create a new customer
     */
    public function createCustomer(array $data, ?int $createdBy = null): bool
    {
        if ($createdBy) {
            $data['created_by'] = $createdBy;
            $data['updated_by'] = $createdBy;
        }

        // Set default status if not provided
        if (!isset($data['status'])) {
            $data['status'] = 'active';
        }

        return $this->insert($data);
    }

    /**
     * Update customer
     */
    public function updateCustomer(int $id, array $data, ?int $updatedBy = null): bool
    {
        if ($updatedBy) {
            $data['updated_by'] = $updatedBy;
        }

        return $this->update($id, $data);
    }

    /**
     * Get customer's full name
     */
    public function getFullName(array $customer): string
    {
        return trim($customer['first_name'] . ' ' . $customer['last_name']);
    }

    /**
     * Get customer transaction history
     */
    public function getCustomerTransactions(int $customerId, int $perPage = 20): array
    {
        $transactionModel = new \App\Models\TransactionModel();
        return $transactionModel->getTransactionsWithDetails($perPage, ['customer_id' => $customerId]);
    }

    /**
     * Search customers by name or code
     */
    public function searchCustomers(string $search, int $limit = 10): array
    {
        return $this->select('id, customer_code, first_name, last_name, phone, email')
            ->where('status', 'active')
            ->groupStart()
                ->like('customer_code', $search)
                ->orLike('first_name', $search)
                ->orLike('last_name', $search)
                ->orLike('phone', $search)
                ->orLike('email', $search)
            ->groupEnd()
            ->orderBy('first_name', 'ASC')
            ->limit($limit)
            ->findAll();
    }

    /**
     * Get customers by location
     */
    public function getCustomersByLocation(int $locationId, int $perPage = 20): array
    {
        return $this->getCustomersWithDetails($perPage, ['location_id' => $locationId]);
    }

    /**
     * Activate customer
     */
    public function activateCustomer(int $id, ?int $updatedBy = null): bool
    {
        return $this->updateCustomer($id, ['status' => 'active'], $updatedBy);
    }

    /**
     * Deactivate customer
     */
    public function deactivateCustomer(int $id, ?int $updatedBy = null): bool
    {
        return $this->updateCustomer($id, ['status' => 'inactive'], $updatedBy);
    }
}
