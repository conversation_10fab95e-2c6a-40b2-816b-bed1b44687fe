<?php

namespace App\Controllers;

use App\Models\DistrictModel;
use App\Models\CountryModel;
use App\Models\ProvinceModel;

class DistrictsController extends BaseController
{
    protected $districtModel;
    protected $countryModel;
    protected $provinceModel;
    protected $data = [];

    public function __construct()
    {
        $this->districtModel = new DistrictModel();
        $this->countryModel = new CountryModel();
        $this->provinceModel = new ProvinceModel();
    }

    /**
     * Display list of districts (GET)
     */
    public function index()
    {
        $search = $this->request->getGet('search') ?? '';
        $provinceId = $this->request->getGet('province_id');
        $perPage = 20;
        
        // Get districts with hierarchy information
        $districts = $this->districtModel->getDistrictsWithHierarchy($perPage, $search, $provinceId);
        $pager = $this->districtModel->pager;
        
        // Get countries and provinces for filter dropdowns
        $countries = $this->countryModel->getForDropdown();
        $provinces = $this->provinceModel->getForDropdown();

        $this->data['title'] = 'Districts Management - DCBuyer Admin';
        $this->data['active_menu'] = 'districts';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Districts', 'url' => base_url('admin/districts')]
        ];
        
        $this->data['page_title'] = 'Districts Management';
        $this->data['page_description'] = 'Manage districts in the location hierarchy.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/districts/create') . '" class="btn btn-admin-primary">
                <i class="fas fa-plus me-2"></i>Add New District
            </a>
        ';
        
        $this->data['districts'] = $districts;
        $this->data['pager'] = $pager;
        $this->data['search'] = $search;
        $this->data['countries'] = $countries;
        $this->data['provinces'] = $provinces;
        $this->data['selected_province'] = $provinceId;
        
        return view('admin/districts/districts_index', $this->data);
    }

    /**
     * Show create district form (GET)
     */
    public function create()
    {
        $this->data['title'] = 'Create District - DCBuyer Admin';
        $this->data['active_menu'] = 'districts';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Districts', 'url' => base_url('admin/districts')],
            ['title' => 'Create District', 'url' => base_url('admin/districts/create')]
        ];

        $this->data['page_title'] = 'Create New District';
        $this->data['page_description'] = 'Add a new district to the location hierarchy.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/districts') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Districts
            </a>
        ';

        // Get countries and provinces for dropdowns
        $this->data['countries'] = $this->countryModel->getForDropdown();
        $this->data['provinces'] = [];

        return view('admin/districts/districts_create', $this->data);
    }

    /**
     * Store new district (POST)
     */
    public function store()
    {
        $rules = [
            'province_id' => 'required|integer|is_not_unique[provinces.id]',
            'name' => 'required|min_length[2]|max_length[100]',
            'code' => 'permit_empty|max_length[10]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'province_id' => $this->request->getPost('province_id'),
            'name' => $this->request->getPost('name'),
            'code' => $this->request->getPost('code') ?: null
        ];
        
        try {
            $result = $this->districtModel->createDistrict($data);
            
            if ($result) {
                return redirect()->to('admin/districts')->with('success', 'District created successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to create district.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating district: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while creating the district.');
        }
    }

    /**
     * View district details (GET)
     */
    public function show($id)
    {
        $district = $this->districtModel->getDistrictWithHierarchy($id);

        if (!$district) {
            return redirect()->to('admin/districts')->with('error', 'District not found.');
        }

        $this->data['district'] = $district;
        $this->data['title'] = 'District Details - DCBuyer Admin';
        $this->data['active_menu'] = 'districts';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Districts', 'url' => base_url('admin/districts')],
            ['title' => 'District Details', 'url' => base_url('admin/districts/' . $id)]
        ];
        
        $this->data['page_title'] = 'District Details: ' . $district['name'];
        $this->data['page_description'] = 'View detailed information about this district.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/districts/' . $id . '/edit') . '" class="btn btn-admin-primary">
                <i class="fas fa-edit me-2"></i>Edit District
            </a>
            <a href="' . base_url('admin/districts') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Districts
            </a>
        ';
        
        return view('admin/districts/districts_show', $this->data);
    }

    /**
     * Show edit district form (GET)
     */
    public function edit($id)
    {
        $district = $this->districtModel->getDistrictWithHierarchy($id);

        if (!$district) {
            return redirect()->to('admin/districts')->with('error', 'District not found.');
        }

        $this->data['district'] = $district;
        $this->data['title'] = 'Edit District - DCBuyer Admin';
        $this->data['active_menu'] = 'districts';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Districts', 'url' => base_url('admin/districts')],
            ['title' => 'Edit District', 'url' => base_url('admin/districts/' . $id . '/edit')]
        ];
        
        $this->data['page_title'] = 'Edit District: ' . $district['name'];
        $this->data['page_description'] = 'Update district information.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/districts/' . $id) . '" class="btn btn-secondary">
                <i class="fas fa-eye me-2"></i>View Details
            </a>
            <a href="' . base_url('admin/districts') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Districts
            </a>
        ';

        // Get countries and provinces for dropdowns
        $this->data['countries'] = $this->countryModel->getForDropdown();
        $this->data['provinces'] = $this->provinceModel->getByCountryForDropdown($district['country_id']);

        return view('admin/districts/districts_edit', $this->data);
    }

    /**
     * Update district (PUT/PATCH)
     */
    public function update($id)
    {
        $district = $this->districtModel->find($id);

        if (!$district) {
            return redirect()->to('admin/districts')->with('error', 'District not found.');
        }
        
        $rules = [
            'province_id' => 'required|integer|is_not_unique[provinces.id]',
            'name' => 'required|min_length[2]|max_length[100]',
            'code' => 'permit_empty|max_length[10]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'province_id' => $this->request->getPost('province_id'),
            'name' => $this->request->getPost('name'),
            'code' => $this->request->getPost('code') ?: null
        ];
        
        try {
            $result = $this->districtModel->updateDistrict($id, $data);
            
            if ($result) {
                return redirect()->to('admin/districts/' . $id)->with('success', 'District updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update district.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error updating district: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating the district.');
        }
    }

    /**
     * Delete district (DELETE)
     */
    public function delete($id)
    {
        $district = $this->districtModel->find($id);

        if (!$district) {
            return redirect()->to('admin/districts')->with('error', 'District not found.');
        }

        try {
            // Check if district has LLGs
            if ($this->districtModel->hasLlgs($id)) {
                return redirect()->to('admin/districts')->with('error', 'Cannot delete district that has LLGs. Please delete all LLGs first.');
            }
            
            $result = $this->districtModel->delete($id);
            
            if ($result) {
                return redirect()->to('admin/districts')->with('success', 'District deleted successfully.');
            } else {
                return redirect()->to('admin/districts')->with('error', 'Failed to delete district.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error deleting district: ' . $e->getMessage());
            return redirect()->to('admin/districts')->with('error', 'An error occurred while deleting the district.');
        }
    }

    // ========================================
    // API Methods for Cascading Dropdowns
    // ========================================

    /**
     * Get districts by province (AJAX)
     */
    public function getByProvince($provinceId)
    {
        if ($this->request->isAJAX()) {
            // Clean output buffer and set headers
            ob_clean();
            $this->response->setContentType('application/json');

            try {
                $districts = $this->districtModel->getByProvinceForDropdown($provinceId);
                return $this->response->setJSON([
                    'success' => true,
                    'data' => $districts
                ]);
            } catch (\Exception $e) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Error fetching districts: ' . $e->getMessage()
                ]);
            }
        }

        return $this->response->setStatusCode(404);
    }
}
