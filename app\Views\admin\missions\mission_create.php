<?= $this->extend('templates/admin_template') ?>

<?= $this->section('title') ?><?= $title ?><?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Display Validation Errors -->
<?php if (session()->getFlashdata('errors')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>Please correct the following errors:</h6>
        <ul class="mb-0">
            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                <li><?= esc($error) ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Mission Create Form -->
<div class="form-section">
    <h5 class="form-section-title">
        <i class="fas fa-plus me-2"></i>Mission Information
    </h5>
            <?= form_open('admin/missions/store', ['class' => 'mission-form']) ?>
            
            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6">
                    <div class="form-subsection">
                        <h6 class="subsection-title">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h6>
                        
                        <div class="mb-3">
                            <label for="mission_name" class="form-label required">Mission Name</label>
                            <input type="text" 
                                   class="form-control <?= $validation->hasError('mission_name') ? 'is-invalid' : '' ?>" 
                                   id="mission_name" 
                                   name="mission_name" 
                                   value="<?= esc($mission['mission_name']) ?>" 
                                   placeholder="Enter mission name"
                                   required>
                            <?php if ($validation->hasError('mission_name')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('mission_name') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="mb-3">
                            <label for="mission_date" class="form-label required">Mission Date</label>
                            <input type="date" 
                                   class="form-control <?= $validation->hasError('mission_date') ? 'is-invalid' : '' ?>" 
                                   id="mission_date" 
                                   name="mission_date" 
                                   value="<?= esc($mission['mission_date']) ?>" 
                                   required>
                            <?php if ($validation->hasError('mission_date')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('mission_date') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="mb-3">
                            <label for="mission_status" class="form-label required">Mission Status</label>
                            <select class="form-select <?= $validation->hasError('mission_status') ? 'is-invalid' : '' ?>" 
                                    id="mission_status" 
                                    name="mission_status" 
                                    required>
                                <option value="">Select Status</option>
                                <option value="pending" <?= $mission['mission_status'] === 'pending' ? 'selected' : '' ?>>Pending</option>
                                <option value="in_progress" <?= $mission['mission_status'] === 'in_progress' ? 'selected' : '' ?>>In Progress</option>
                                <option value="completed" <?= $mission['mission_status'] === 'completed' ? 'selected' : '' ?>>Completed</option>
                                <option value="cancelled" <?= $mission['mission_status'] === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                            </select>
                            <?php if ($validation->hasError('mission_status')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('mission_status') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Assignment Information -->
                <div class="col-md-6">
                    <div class="form-subsection">
                        <h6 class="subsection-title">
                            <i class="fas fa-users me-2"></i>Assignment Information
                        </h6>
                        
                        <div class="mb-3">
                            <label for="user_id" class="form-label">Assigned Buyer</label>
                            <select class="form-select <?= $validation->hasError('user_id') ? 'is-invalid' : '' ?>" 
                                    id="user_id" 
                                    name="user_id">
                                <option value="">Select Buyer (Optional)</option>
                                <?php foreach ($buyers as $buyer): ?>
                                    <option value="<?= $buyer['id'] ?>" <?= $mission['user_id'] == $buyer['id'] ? 'selected' : '' ?>>
                                        <?= esc($buyer['fullname']) ?> (@<?= esc($buyer['username']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if ($validation->hasError('user_id')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('user_id') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="mb-3">
                            <label for="commodity_id" class="form-label">Commodity</label>
                            <select class="form-select <?= $validation->hasError('commodity_id') ? 'is-invalid' : '' ?>" 
                                    id="commodity_id" 
                                    name="commodity_id">
                                <option value="">Select Commodity (Optional)</option>
                                <?php foreach ($commodities as $commodity): ?>
                                    <option value="<?= $commodity['commodity_id'] ?>" <?= $mission['commodity_id'] == $commodity['commodity_id'] ? 'selected' : '' ?>>
                                        <?= esc($commodity['commodity_name']) ?> (<?= esc($commodity['unit_of_measurement']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if ($validation->hasError('commodity_id')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('commodity_id') ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="mb-3">
                            <label for="location_id" class="form-label">Mission Location</label>
                            <select class="form-select <?= $validation->hasError('location_id') ? 'is-invalid' : '' ?>" 
                                    id="location_id" 
                                    name="location_id">
                                <option value="">Select Location (Optional)</option>
                                <?php foreach ($locations as $location): ?>
                                    <option value="<?= $location['id'] ?>" <?= $mission['location_id'] == $location['id'] ? 'selected' : '' ?>>
                                        <?= esc($location['hierarchy']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <?php if ($validation->hasError('location_id')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('location_id') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Financial Information -->
                <div class="col-md-6">
                    <div class="form-subsection">
                        <h6 class="subsection-title">
                            <i class="fas fa-coins me-2"></i>Financial Information
                        </h6>
                        
                        <div class="mb-3">
                            <label for="budgeted_amount" class="form-label">Budgeted Amount</label>
                            <div class="input-group">
                                <span class="input-group-text">PGK</span>
                                <input type="number" 
                                       class="form-control <?= $validation->hasError('budgeted_amount') ? 'is-invalid' : '' ?>" 
                                       id="budgeted_amount" 
                                       name="budgeted_amount" 
                                       value="<?= esc($mission['budgeted_amount']) ?>" 
                                       placeholder="0.00"
                                       step="0.01"
                                       min="0">
                                <?php if ($validation->hasError('budgeted_amount')): ?>
                                    <div class="invalid-feedback">
                                        <?= $validation->getError('budgeted_amount') ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="actual_amount" class="form-label">Actual Amount</label>
                            <div class="input-group">
                                <span class="input-group-text">PGK</span>
                                <input type="number" 
                                       class="form-control <?= $validation->hasError('actual_amount') ? 'is-invalid' : '' ?>" 
                                       id="actual_amount" 
                                       name="actual_amount" 
                                       value="<?= esc($mission['actual_amount']) ?>" 
                                       placeholder="0.00"
                                       step="0.01"
                                       min="0">
                                <?php if ($validation->hasError('actual_amount')): ?>
                                    <div class="invalid-feedback">
                                        <?= $validation->getError('actual_amount') ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="col-md-6">
                    <div class="form-subsection">
                        <h6 class="subsection-title">
                            <i class="fas fa-sticky-note me-2"></i>Additional Information
                        </h6>
                        
                        <div class="mb-3">
                            <label for="remarks" class="form-label">Remarks</label>
                            <textarea class="form-control <?= $validation->hasError('remarks') ? 'is-invalid' : '' ?>" 
                                      id="remarks" 
                                      name="remarks" 
                                      rows="5" 
                                      placeholder="Enter any additional notes or remarks..."><?= esc($mission['remarks']) ?></textarea>
                            <?php if ($validation->hasError('remarks')): ?>
                                <div class="invalid-feedback">
                                    <?= $validation->getError('remarks') ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="submit" class="btn btn-admin-primary">
                    <i class="fas fa-save me-2"></i>Create Mission
                </button>
                <a href="<?= base_url('admin/missions') ?>" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
            </div>

    <?= form_close() ?>
</div>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.form-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #f1f3f4;
}

.form-subsection {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.subsection-title {
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

.form-label.required::after {
    content: " *";
    color: #dc3545;
}

.form-control:focus, .form-select:focus {
    border-color: #2E7D32;
    box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
}

.form-actions {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
    text-align: center;
}

.form-actions .btn {
    margin: 0 0.5rem;
    min-width: 120px;
}

.input-group-text {
    background-color: #e9ecef;
    border-color: #ced4da;
}

@media (max-width: 768px) {
    .form-actions .btn {
        display: block;
        width: 100%;
        margin: 0.25rem 0;
    }
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set default mission date to today if empty
    const missionDateInput = document.getElementById('mission_date');
    if (!missionDateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        missionDateInput.value = today;
    }
    
    // Form validation
    const form = document.querySelector('.mission-form');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Validate required fields
        const requiredFields = ['mission_name', 'mission_date', 'mission_status'];
        requiredFields.forEach(fieldName => {
            const field = document.getElementById(fieldName);
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});
</script>
<?= $this->endSection() ?>
