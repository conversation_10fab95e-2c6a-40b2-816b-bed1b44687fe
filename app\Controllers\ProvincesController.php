<?php

namespace App\Controllers;

use App\Models\ProvinceModel;
use App\Models\CountryModel;

class ProvincesController extends BaseController
{
    protected $provinceModel;
    protected $countryModel;
    protected $data = [];

    public function __construct()
    {
        $this->provinceModel = new ProvinceModel();
        $this->countryModel = new CountryModel();
    }

    /**
     * Display list of provinces (GET)
     */
    public function index()
    {
        $search = $this->request->getGet('search') ?? '';
        $countryId = $this->request->getGet('country_id');
        $perPage = 20;
        
        // Get provinces with country information
        $provinces = $this->provinceModel->getProvincesWithCountry($perPage, $search, $countryId);
        $pager = $this->provinceModel->pager;
        
        // Get countries for filter dropdown
        $countries = $this->countryModel->getForDropdown();

        $this->data['title'] = 'Provinces Management - DCBuyer Admin';
        $this->data['active_menu'] = 'provinces';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Provinces', 'url' => base_url('admin/provinces')]
        ];
        
        $this->data['page_title'] = 'Provinces Management';
        $this->data['page_description'] = 'Manage provinces in the location hierarchy.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/provinces/create') . '" class="btn btn-admin-primary">
                <i class="fas fa-plus me-2"></i>Add New Province
            </a>
        ';
        
        $this->data['provinces'] = $provinces;
        $this->data['pager'] = $pager;
        $this->data['search'] = $search;
        $this->data['countries'] = $countries;
        $this->data['selected_country'] = $countryId;
        
        return view('admin/provinces/provinces_index', $this->data);
    }

    /**
     * Show create province form (GET)
     */
    public function create()
    {
        $this->data['title'] = 'Create Province - DCBuyer Admin';
        $this->data['active_menu'] = 'provinces';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Provinces', 'url' => base_url('admin/provinces')],
            ['title' => 'Create Province', 'url' => base_url('admin/provinces/create')]
        ];

        $this->data['page_title'] = 'Create New Province';
        $this->data['page_description'] = 'Add a new province to the location hierarchy.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/provinces') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Provinces
            </a>
        ';

        // Get countries for dropdown
        $this->data['countries'] = $this->countryModel->getForDropdown();

        return view('admin/provinces/provinces_create', $this->data);
    }

    /**
     * Store new province (POST)
     */
    public function store()
    {
        $rules = [
            'country_id' => 'required|integer|is_not_unique[countries.id]',
            'name' => 'required|min_length[2]|max_length[100]',
            'code' => 'permit_empty|max_length[10]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'country_id' => $this->request->getPost('country_id'),
            'name' => $this->request->getPost('name'),
            'code' => $this->request->getPost('code') ?: null
        ];
        
        try {
            $result = $this->provinceModel->createProvince($data);
            
            if ($result) {
                return redirect()->to('admin/provinces')->with('success', 'Province created successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to create province.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating province: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while creating the province.');
        }
    }

    /**
     * View province details (GET)
     */
    public function show($id)
    {
        $province = $this->provinceModel->getProvinceWithCountry($id);

        if (!$province) {
            return redirect()->to('admin/provinces')->with('error', 'Province not found.');
        }

        $this->data['province'] = $province;
        $this->data['title'] = 'Province Details - DCBuyer Admin';
        $this->data['active_menu'] = 'provinces';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Provinces', 'url' => base_url('admin/provinces')],
            ['title' => 'Province Details', 'url' => base_url('admin/provinces/' . $id)]
        ];
        
        $this->data['page_title'] = 'Province Details: ' . $province['name'];
        $this->data['page_description'] = 'View detailed information about this province.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/provinces/' . $id . '/edit') . '" class="btn btn-admin-primary">
                <i class="fas fa-edit me-2"></i>Edit Province
            </a>
            <a href="' . base_url('admin/provinces') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Provinces
            </a>
        ';
        
        return view('admin/provinces/provinces_show', $this->data);
    }

    /**
     * Show edit province form (GET)
     */
    public function edit($id)
    {
        $province = $this->provinceModel->getProvinceWithCountry($id);

        if (!$province) {
            return redirect()->to('admin/provinces')->with('error', 'Province not found.');
        }

        $this->data['province'] = $province;
        $this->data['title'] = 'Edit Province - DCBuyer Admin';
        $this->data['active_menu'] = 'provinces';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'Provinces', 'url' => base_url('admin/provinces')],
            ['title' => 'Edit Province', 'url' => base_url('admin/provinces/' . $id . '/edit')]
        ];
        
        $this->data['page_title'] = 'Edit Province: ' . $province['name'];
        $this->data['page_description'] = 'Update province information.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/provinces/' . $id) . '" class="btn btn-secondary">
                <i class="fas fa-eye me-2"></i>View Details
            </a>
            <a href="' . base_url('admin/provinces') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Provinces
            </a>
        ';

        // Get countries for dropdown
        $this->data['countries'] = $this->countryModel->getForDropdown();

        return view('admin/provinces/provinces_edit', $this->data);
    }

    /**
     * Update province (PUT/PATCH)
     */
    public function update($id)
    {
        $province = $this->provinceModel->find($id);

        if (!$province) {
            return redirect()->to('admin/provinces')->with('error', 'Province not found.');
        }
        
        $rules = [
            'country_id' => 'required|integer|is_not_unique[countries.id]',
            'name' => 'required|min_length[2]|max_length[100]',
            'code' => 'permit_empty|max_length[10]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'country_id' => $this->request->getPost('country_id'),
            'name' => $this->request->getPost('name'),
            'code' => $this->request->getPost('code') ?: null
        ];
        
        try {
            $result = $this->provinceModel->updateProvince($id, $data);
            
            if ($result) {
                return redirect()->to('admin/provinces/' . $id)->with('success', 'Province updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update province.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error updating province: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating the province.');
        }
    }

    /**
     * Delete province (DELETE)
     */
    public function delete($id)
    {
        $province = $this->provinceModel->find($id);

        if (!$province) {
            return redirect()->to('admin/provinces')->with('error', 'Province not found.');
        }

        try {
            // Check if province has districts
            if ($this->provinceModel->hasDistricts($id)) {
                return redirect()->to('admin/provinces')->with('error', 'Cannot delete province that has districts. Please delete all districts first.');
            }
            
            $result = $this->provinceModel->delete($id);
            
            if ($result) {
                return redirect()->to('admin/provinces')->with('success', 'Province deleted successfully.');
            } else {
                return redirect()->to('admin/provinces')->with('error', 'Failed to delete province.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error deleting province: ' . $e->getMessage());
            return redirect()->to('admin/provinces')->with('error', 'An error occurred while deleting the province.');
        }
    }
}
