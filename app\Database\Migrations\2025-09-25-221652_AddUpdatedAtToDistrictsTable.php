<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddUpdatedAtToDistrictsTable extends Migration
{
    public function up()
    {
        // Add updated_at column to districts table
        $this->forge->addColumn('districts', [
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'after' => 'created_at'
            ]
        ]);

        // Set default timestamp for updated_at
        $this->db->query('ALTER TABLE districts ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP');

        // Update existing records to have the same updated_at as created_at
        $this->db->query('UPDATE districts SET updated_at = created_at WHERE updated_at IS NULL');
    }

    public function down()
    {
        // Remove updated_at column
        $this->forge->dropColumn('districts', 'updated_at');
    }
}
