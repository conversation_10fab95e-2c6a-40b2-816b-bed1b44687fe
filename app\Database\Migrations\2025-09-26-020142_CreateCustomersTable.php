<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateCustomersTable extends Migration
{
    public function up()
    {
        // Create customers table
        $this->forge->addField([
            'id' => [
                'type' => 'SERIAL',
            ],
            'customer_code' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'unique' => true,
            ],
            'first_name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
            ],
            'last_name' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
            ],
            'phone' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            'email' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
            ],
            'address' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'location_id' => [
                'type' => 'BIGINT',
                'null' => true,
            ],
            'status' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'default' => 'active',
            ],
            'created_by' => [
                'type' => 'BIGINT',
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'NOW()',
            ],
            'updated_by' => [
                'type' => 'BIGINT',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'null' => false,
                'default' => 'NOW()',
            ],
            'deleted_by' => [
                'type' => 'BIGINT',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'is_deleted' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
        ]);

        $this->forge->addPrimaryKey('id');
        $this->forge->addUniqueKey('customer_code');

        // Add indexes for performance
        $this->forge->addKey(['first_name', 'last_name']);
        $this->forge->addKey('phone');
        $this->forge->addKey('email');
        $this->forge->addKey('location_id');
        $this->forge->addKey('status');
        $this->forge->addKey('created_at');
        $this->forge->addKey('is_deleted');

        $this->forge->createTable('customers');

        // Add foreign key constraints
        $this->forge->addForeignKey('location_id', 'locations', 'id', 'CASCADE', 'SET NULL');
        $this->forge->addForeignKey('created_by', 'users', 'id', 'CASCADE', 'SET NULL');
    }

    public function down()
    {
        // Drop the customers table
        $this->forge->dropTable('customers');
    }
}
