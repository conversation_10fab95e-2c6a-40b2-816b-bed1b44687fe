<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>

<!-- Transaction Form -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Edit Transaction Information</h5>
            </div>
            <div class="card-body">
                <?= form_open('admin/transactions/' . $transaction['id'], ['method' => 'PUT', 'id' => 'transactionForm']) ?>
                    <?= csrf_field() ?>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="user_id" class="form-label">Buyer <span class="text-danger">*</span></label>
                                <select class="form-control <?= isset($errors['user_id']) ? 'is-invalid' : '' ?>" 
                                        id="user_id" name="user_id" required>
                                    <option value="">Select Buyer</option>
                                    <?php foreach ($buyers as $buyer): ?>
                                        <option value="<?= $buyer['id'] ?>" 
                                                <?= $transaction['user_id'] == $buyer['id'] ? 'selected' : '' ?>>
                                            <?= esc($buyer['fullname']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($errors['user_id'])): ?>
                                    <div class="invalid-feedback"><?= $errors['user_id'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="mission_id" class="form-label">Mission <span class="text-danger">*</span></label>
                                <select class="form-control <?= isset($errors['mission_id']) ? 'is-invalid' : '' ?>" 
                                        id="mission_id" name="mission_id" required>
                                    <option value="">Select Mission</option>
                                    <?php foreach ($missions as $mission): ?>
                                        <option value="<?= $mission['id'] ?>" 
                                                data-user-id="<?= $mission['user_id'] ?>"
                                                data-commodity-id="<?= $mission['commodity_id'] ?>"
                                                <?= $transaction['mission_id'] == $mission['id'] ? 'selected' : '' ?>>
                                            <?= esc($mission['mission_number']) ?> - <?= esc($mission['mission_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($errors['mission_id'])): ?>
                                    <div class="invalid-feedback"><?= $errors['mission_id'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="commodity_id" class="form-label">Commodity <span class="text-danger">*</span></label>
                                <select class="form-control <?= isset($errors['commodity_id']) ? 'is-invalid' : '' ?>" 
                                        id="commodity_id" name="commodity_id" required>
                                    <option value="">Select Commodity</option>
                                    <?php foreach ($commodities as $commodity): ?>
                                        <option value="<?= $commodity['commodity_id'] ?>" 
                                                data-unit="<?= esc($commodity['unit_of_measurement']) ?>"
                                                <?= $transaction['commodity_id'] == $commodity['commodity_id'] ? 'selected' : '' ?>>
                                            <?= esc($commodity['commodity_name']) ?> (<?= esc($commodity['unit_of_measurement']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($errors['commodity_id'])): ?>
                                    <div class="invalid-feedback"><?= $errors['commodity_id'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="customer_id" class="form-label">Customer (Optional)</label>
                                <select class="form-control <?= isset($errors['customer_id']) ? 'is-invalid' : '' ?>" 
                                        id="customer_id" name="customer_id">
                                    <option value="">No Customer</option>
                                    <?php foreach ($customers as $customer): ?>
                                        <option value="<?= $customer['id'] ?>" 
                                                <?= $transaction['customer_id'] == $customer['id'] ? 'selected' : '' ?>>
                                            <?= esc($customer['first_name'] . ' ' . $customer['last_name']) ?> (<?= esc($customer['customer_code']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($errors['customer_id'])): ?>
                                    <div class="invalid-feedback"><?= $errors['customer_id'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="location_id" class="form-label">Location <span class="text-danger">*</span></label>
                        <select class="form-control <?= isset($errors['location_id']) ? 'is-invalid' : '' ?>" 
                                id="location_id" name="location_id" required>
                            <option value="">Select Location</option>
                            <?php foreach ($locations as $location): ?>
                                <option value="<?= $location['id'] ?>" 
                                        <?= $transaction['location_id'] == $location['id'] ? 'selected' : '' ?>>
                                    <?= esc($location['hierarchy']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($errors['location_id'])): ?>
                            <div class="invalid-feedback"><?= $errors['location_id'] ?></div>
                        <?php endif; ?>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="quantity" class="form-label">Quantity <span class="text-danger">*</span></label>
                                <input type="number" step="0.001" min="0.001" 
                                       class="form-control <?= isset($errors['quantity']) ? 'is-invalid' : '' ?>" 
                                       id="quantity" name="quantity" 
                                       value="<?= $transaction['quantity'] ?>" 
                                       placeholder="0.000" required>
                                <?php if (isset($errors['quantity'])): ?>
                                    <div class="invalid-feedback"><?= $errors['quantity'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="unit_price" class="form-label">Unit Price <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">PGK</span>
                                    <input type="number" step="0.01" min="0.01" 
                                           class="form-control <?= isset($errors['unit_price']) ? 'is-invalid' : '' ?>" 
                                           id="unit_price" name="unit_price" 
                                           value="<?= $transaction['unit_price'] ?>" 
                                           placeholder="0.00" required>
                                </div>
                                <?php if (isset($errors['unit_price'])): ?>
                                    <div class="invalid-feedback"><?= $errors['unit_price'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label for="payment_amount" class="form-label">Total Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">PGK</span>
                                    <input type="number" step="0.01" min="0.01" 
                                           class="form-control <?= isset($errors['payment_amount']) ? 'is-invalid' : '' ?>" 
                                           id="payment_amount" name="payment_amount" 
                                           value="<?= $transaction['payment_amount'] ?>" 
                                           placeholder="0.00" readonly required>
                                </div>
                                <?php if (isset($errors['payment_amount'])): ?>
                                    <div class="invalid-feedback"><?= $errors['payment_amount'] ?></div>
                                <?php endif; ?>
                                <small class="form-text text-muted">Automatically calculated: Quantity × Unit Price</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="transaction_date" class="form-label">Transaction Date <span class="text-danger">*</span></label>
                                <input type="date" 
                                       class="form-control <?= isset($errors['transaction_date']) ? 'is-invalid' : '' ?>" 
                                       id="transaction_date" name="transaction_date" 
                                       value="<?= $transaction['transaction_date'] ?>" 
                                       required>
                                <?php if (isset($errors['transaction_date'])): ?>
                                    <div class="invalid-feedback"><?= $errors['transaction_date'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-control <?= isset($errors['status']) ? 'is-invalid' : '' ?>" 
                                        id="status" name="status" required>
                                    <option value="pending" <?= $transaction['status'] === 'pending' ? 'selected' : '' ?>>Pending</option>
                                    <option value="completed" <?= $transaction['status'] === 'completed' ? 'selected' : '' ?>>Completed</option>
                                    <option value="cancelled" <?= $transaction['status'] === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                                </select>
                                <?php if (isset($errors['status'])): ?>
                                    <div class="invalid-feedback"><?= $errors['status'] ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="remarks" class="form-label">Remarks</label>
                        <textarea class="form-control <?= isset($errors['remarks']) ? 'is-invalid' : '' ?>" 
                                  id="remarks" name="remarks" rows="3" 
                                  placeholder="Additional notes or comments..."><?= esc($transaction['remarks']) ?></textarea>
                        <?php if (isset($errors['remarks'])): ?>
                            <div class="invalid-feedback"><?= $errors['remarks'] ?></div>
                        <?php endif; ?>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Update Transaction
                        </button>
                        <a href="<?= base_url('admin/transactions/' . $transaction['id']) ?>" class="btn btn-info">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        <a href="<?= base_url('admin/transactions') ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                    </div>

                <?= form_close() ?>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Current Transaction</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td class="fw-bold">Code:</td>
                        <td><?= esc($transaction['transaction_code']) ?></td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Created:</td>
                        <td><?= date('M d, Y', strtotime($transaction['created_at'])) ?></td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Last Updated:</td>
                        <td><?= date('M d, Y', strtotime($transaction['updated_at'])) ?></td>
                    </tr>
                    <tr>
                        <td class="fw-bold">Current Status:</td>
                        <td>
                            <?php
                            $statusClass = [
                                'pending' => 'badge-warning',
                                'completed' => 'badge-success',
                                'cancelled' => 'badge-danger'
                            ];
                            ?>
                            <span class="badge <?= $statusClass[$transaction['status']] ?? 'badge-secondary' ?>">
                                <?= ucfirst($transaction['status']) ?>
                            </span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Edit Guidelines</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> Important Notes</h6>
                    <ul class="mb-0">
                        <li>Changing amounts will update mission budget</li>
                        <li>Buyer and mission must match assignments</li>
                        <li>Commodity must match mission commodity</li>
                        <li>Status changes affect reporting</li>
                        <li>All changes are logged for audit</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const userSelect = document.getElementById('user_id');
    const missionSelect = document.getElementById('mission_id');
    const commoditySelect = document.getElementById('commodity_id');
    const quantityInput = document.getElementById('quantity');
    const unitPriceInput = document.getElementById('unit_price');
    const paymentAmountInput = document.getElementById('payment_amount');

    // Filter missions based on selected user
    userSelect.addEventListener('change', function() {
        const selectedUserId = this.value;
        const missionOptions = missionSelect.querySelectorAll('option');
        
        missionOptions.forEach(option => {
            if (option.value === '') {
                option.style.display = 'block';
                return;
            }
            
            const missionUserId = option.getAttribute('data-user-id');
            if (selectedUserId === '' || missionUserId === selectedUserId) {
                option.style.display = 'block';
            } else {
                option.style.display = 'none';
            }
        });
        
        // Reset mission selection if current selection is not valid
        const currentMission = missionSelect.value;
        if (currentMission) {
            const currentOption = missionSelect.querySelector(`option[value="${currentMission}"]`);
            if (currentOption && currentOption.style.display === 'none') {
                missionSelect.value = '';
                commoditySelect.value = '';
            }
        }
    });

    // Set commodity based on selected mission
    missionSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            const commodityId = selectedOption.getAttribute('data-commodity-id');
            if (commodityId) {
                commoditySelect.value = commodityId;
            }
        } else {
            commoditySelect.value = '';
        }
    });

    // Calculate total amount
    function calculateTotal() {
        const quantity = parseFloat(quantityInput.value) || 0;
        const unitPrice = parseFloat(unitPriceInput.value) || 0;
        const total = quantity * unitPrice;
        paymentAmountInput.value = total.toFixed(2);
    }

    quantityInput.addEventListener('input', calculateTotal);
    unitPriceInput.addEventListener('input', calculateTotal);

    // Trigger initial filtering if user is pre-selected
    if (userSelect.value) {
        userSelect.dispatchEvent(new Event('change'));
    }
});
</script>

<?= $this->endSection() ?>
