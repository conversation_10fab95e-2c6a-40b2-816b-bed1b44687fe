<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\HTTP\ResponseInterface;

class SimpleApiController extends ResourceController
{
    protected $format = 'json';
    
    public function __construct()
    {
        // Disable session handling for API endpoints
        // This prevents session-related errors from contaminating JSON responses
    }
    
    public function getDistricts($provinceId = null)
    {
        // Temporarily disable error display to prevent HTML contamination
        $old_display_errors = ini_get('display_errors');
        ini_set('display_errors', '0');
        
        // Clean any output buffers to prevent HTML contamination
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        // Start clean output buffering
        ob_start();
        
        // Set headers directly
        header('Content-Type: application/json');
        header('Cache-Control: no-cache, must-revalidate');
        
        try {
            // Validate province ID
            if (!$provinceId || !is_numeric($provinceId)) {
                $response = [
                    'success' => false,
                    'message' => 'Invalid province ID provided'
                ];
                
                ob_clean();
                echo json_encode($response);
                
                // Restore error display setting
                ini_set('display_errors', $old_display_errors);
                exit;
            }

            // Use CodeIgniter's database service
            $db = \Config\Database::connect();
            
            // Simple query to get districts by province_id
            $query = $db->query("SELECT id, name FROM districts WHERE province_id = ? ORDER BY name", [$provinceId]);
            $districts = $query->getResultArray();

            $response = [
                'success' => true,
                'data' => $districts
            ];
            
            // Clean output and send only JSON
            ob_clean();
            echo json_encode($response);
            
            // Restore error display setting
            ini_set('display_errors', $old_display_errors);
            exit;

        } catch (\Exception $e) {
            // Log the error for debugging
            error_log('SimpleApiController::getDistricts - ' . $e->getMessage());

            $response = [
                'success' => false,
                'message' => 'Error loading districts'
            ];
            
            ob_clean();
            echo json_encode($response);
            
            // Restore error display setting
            ini_set('display_errors', $old_display_errors);
            exit;
        }
    }
}
