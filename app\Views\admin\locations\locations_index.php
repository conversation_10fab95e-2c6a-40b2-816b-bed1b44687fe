<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<!-- SheetJS for Excel export -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 1.5rem;
        color: white;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .stats-card .stats-icon {
        font-size: 2.5rem;
        opacity: 0.8;
    }

    .stats-card .stats-number {
        font-size: 2rem;
        font-weight: 700;
        margin: 0.5rem 0;
    }

    .stats-card .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .table-controls {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .table-responsive {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .table th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
        padding: 1rem 0.75rem;
    }

    .table td {
        padding: 0.75rem;
        vertical-align: middle;
    }

    .badge-location {
        background-color: #e3f2fd;
        color: #1976d2;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .btn-action {
        padding: 0.25rem 0.5rem;
        margin: 0 0.125rem;
        border-radius: 6px;
        font-size: 0.875rem;
    }

    .location-hierarchy {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .gps-coordinates {
        font-family: monospace;
        font-size: 0.8rem;
        background-color: #f8f9fa;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="stats-icon me-3">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div>
                        <div class="stats-number"><?= number_format($stats['total_locations']) ?></div>
                        <div class="stats-label">Total Locations</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="d-flex align-items-center">
                    <div class="stats-icon me-3">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div>
                        <div class="stats-number"><?= number_format($stats['recent_locations']) ?></div>
                        <div class="stats-label">Recent (30 days)</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="d-flex align-items-center">
                    <div class="stats-icon me-3">
                        <i class="fas fa-satellite"></i>
                    </div>
                    <div>
                        <div class="stats-number"><?= number_format($stats['locations_with_gps']) ?></div>
                        <div class="stats-label">With GPS Coordinates</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                <div class="d-flex align-items-center">
                    <div class="stats-icon me-3">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div>
                        <div class="stats-number"><?= $stats['total_locations'] > 0 ? number_format(($stats['locations_with_gps'] / $stats['total_locations']) * 100, 1) : 0 ?>%</div>
                        <div class="stats-label">GPS Coverage</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Controls -->
    <div class="table-controls">
        <form method="GET" action="<?= base_url('admin/locations') ?>">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search Locations</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?= esc($search) ?>" placeholder="Search by name, ward, or location...">
                </div>
                <div class="col-md-2">
                    <label for="country_id" class="form-label">Country</label>
                    <select class="form-select" id="country_id" name="country_id">
                        <option value="">All Countries</option>
                        <?php foreach ($countries as $country): ?>
                            <option value="<?= $country['id'] ?>" <?= $filters['country_id'] == $country['id'] ? 'selected' : '' ?>>
                                <?= esc($country['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="province_id" class="form-label">Province</label>
                    <select class="form-select" id="province_id" name="province_id">
                        <option value="">All Provinces</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="district_id" class="form-label">District</label>
                    <select class="form-select" id="district_id" name="district_id">
                        <option value="">All Districts</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="llg_id" class="form-label">LLG</label>
                    <select class="form-select" id="llg_id" name="llg_id">
                        <option value="">All LLGs</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                    <a href="<?= base_url('admin/locations') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>Clear
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Locations Table -->
    <div class="table-responsive">
        <table id="locationsTable" class="table table-hover mb-0">
            <thead>
                <tr>
                    <th>Location Name</th>
                    <th>Ward</th>
                    <th>Hierarchy</th>
                    <th>GPS Coordinates</th>
                    <th>Created By</th>
                    <th>Created Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($locations)): ?>
                    <?php foreach ($locations as $location): ?>
                        <tr>
                            <td>
                                <strong><?= esc($location['location_name']) ?></strong>
                                <?php if (!empty($location['remarks'])): ?>
                                    <br><small class="text-muted"><?= esc(substr($location['remarks'], 0, 50)) ?><?= strlen($location['remarks']) > 50 ? '...' : '' ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge-location"><?= esc($location['ward']) ?></span>
                            </td>
                            <td>
                                <div class="location-hierarchy">
                                    <div><?= esc($location['country_name']) ?></div>
                                    <div><?= esc($location['province_name']) ?></div>
                                    <div><?= esc($location['district_name']) ?></div>
                                    <div><?= esc($location['llg_name']) ?></div>
                                </div>
                            </td>
                            <td>
                                <?php if (!empty($location['gps_latitude']) && !empty($location['gps_longitude'])): ?>
                                    <div class="gps-coordinates">
                                        <?= number_format($location['gps_latitude'], 6) ?>,<br>
                                        <?= number_format($location['gps_longitude'], 6) ?>
                                    </div>
                                <?php else: ?>
                                    <span class="text-muted">Not available</span>
                                <?php endif; ?>
                            </td>
                            <td><?= esc($location['created_by_name']) ?></td>
                            <td><?= date('M j, Y', strtotime($location['created_at'])) ?></td>
                            <td>
                                <a href="<?= base_url('admin/locations/' . $location['id']) ?>" 
                                   class="btn btn-sm btn-outline-primary btn-action" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?= base_url('admin/locations/' . $location['id'] . '/edit') ?>" 
                                   class="btn btn-sm btn-outline-warning btn-action" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger btn-action" 
                                        onclick="confirmDelete(<?= $location['id'] ?>, '<?= esc($location['location_name']) ?>')" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php else: ?>
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-map-marker-alt fa-3x mb-3"></i>
                                <h5>No locations found</h5>
                                <p>No locations match your search criteria.</p>
                                <a href="<?= base_url('admin/locations/create') ?>" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Add First Location
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <?php if ($pager): ?>
        <div class="d-flex justify-content-between align-items-center mt-4">
            <div class="table-info">
                Showing <?= $pager->getPerPage() ?> entries per page
            </div>
            <div>
                <?= $pager->links() ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the location "<span id="locationName"></span>"?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete Location</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize enhanced table
    initializeEnhancedTable('locationsTable', {
        searchPlaceholder: 'Search locations by name, ward, or hierarchy...',
        exportFilename: 'locations_export',
        rowsPerPage: 25
    });
});

function confirmDelete(locationId, locationName) {
    document.getElementById('locationName').textContent = locationName;
    document.getElementById('deleteForm').action = '<?= base_url('admin/locations') ?>/' + locationId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Enhanced Table Implementation
function initializeEnhancedTable(tableId, options = {}) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const config = {
        searchPlaceholder: options.searchPlaceholder || 'Search...',
        exportFilename: options.exportFilename || 'table_export',
        rowsPerPage: options.rowsPerPage || 25,
        ...options
    };

    addTableSearch(tableId, config);
    addExportButton(tableId, config);
    addTablePagination(tableId, config);
}

function addTableSearch(tableId, config) {
    const table = document.getElementById(tableId);
    const tableContainer = table.closest('.table-responsive').parentNode;

    const searchContainer = document.createElement('div');
    searchContainer.className = 'table-controls mb-3 d-flex justify-content-between align-items-center';

    const searchDiv = document.createElement('div');
    searchDiv.className = 'd-flex align-items-center';
    searchDiv.innerHTML = `
        <label class="me-2 mb-0">Search:</label>
        <input type="text" id="${tableId}Search" class="form-control" style="width: 300px;" placeholder="${config.searchPlaceholder}">
    `;

    const exportDiv = document.createElement('div');
    exportDiv.innerHTML = `
        <button type="button" id="${tableId}ExportExcel" class="btn btn-success">
            <i class="fas fa-file-excel me-2"></i>Export to Excel
        </button>
    `;

    searchContainer.appendChild(searchDiv);
    searchContainer.appendChild(exportDiv);
    tableContainer.insertBefore(searchContainer, tableContainer.firstChild);

    const searchInput = document.getElementById(`${tableId}Search`);
    searchInput.addEventListener('input', function() {
        filterTable(tableId, this.value.toLowerCase());
    });
}

function filterTable(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    updateTableInfo(tableId);
}

function addExportButton(tableId, config) {
    const exportBtn = document.getElementById(`${tableId}ExportExcel`);
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportTableToExcel(tableId, config.exportFilename);
        });
    }
}

function exportTableToExcel(tableId, filename) {
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tr');
    const data = [];

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll(index === 0 ? 'th' : 'td');
        const rowData = [];

        cells.forEach((cell, cellIndex) => {
            if (cellIndex < cells.length - 1) {
                let cellText = cell.textContent.trim();
                if (cell.querySelector('.badge')) {
                    cellText = cell.querySelector('.badge').textContent.trim();
                }
                rowData.push(cellText);
            }
        });

        if (rowData.length > 0) {
            data.push(rowData);
        }
    });

    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(data);

    const colWidths = data[0].map(() => ({ wch: 15 }));
    ws['!cols'] = colWidths;

    const headerRange = XLSX.utils.decode_range(ws['!ref']);
    for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!ws[cellAddress]) continue;
        ws[cellAddress].s = {
            font: { bold: true, color: { rgb: "FFFFFF" } },
            fill: { fgColor: { rgb: "2E7D32" } },
            alignment: { horizontal: "center" }
        };
    }

    XLSX.utils.book_append_sheet(wb, ws, "Locations Export");

    const exportFilename = filename + '_' + new Date().toISOString().split('T')[0] + '.xlsx';
    XLSX.writeFile(wb, exportFilename);

    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>Excel file exported successfully!
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.container-fluid').firstChild);

    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

function addTablePagination(tableId, config) {
    const table = document.getElementById(tableId);
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    if (rows.length <= config.rowsPerPage) return;

    let currentPage = 1;
    const rowsPerPage = config.rowsPerPage;
    const totalPages = Math.ceil(rows.length / rowsPerPage);

    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'table-pagination mt-3 d-flex justify-content-between align-items-center';
    paginationContainer.innerHTML = `
        <div class="table-info">
            <span id="${tableId}TableInfo">Showing 1 to ${Math.min(rowsPerPage, rows.length)} of ${rows.length} entries</span>
        </div>
        <nav>
            <ul class="pagination mb-0" id="${tableId}TablePagination">
            </ul>
        </nav>
    `;

    table.parentNode.appendChild(paginationContainer);

    function showPage(page) {
        const start = (page - 1) * rowsPerPage;
        const end = start + rowsPerPage;

        rows.forEach((row, index) => {
            if (index >= start && index < end && row.style.display !== 'none') {
                row.style.display = '';
            } else if (row.style.display !== 'none') {
                row.style.display = 'none';
            }
        });

        updatePagination(tableId, page, totalPages, showPage);
        updateTableInfo(tableId);
    }

    showPage(1);
}

function updatePagination(tableId, currentPage, totalPages, showPageCallback) {
    const pagination = document.getElementById(`${tableId}TablePagination`);
    pagination.innerHTML = '';

    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>`;
    pagination.appendChild(prevLi);

    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
        pagination.appendChild(li);
    }

    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>`;
    pagination.appendChild(nextLi);

    pagination.addEventListener('click', function(e) {
        e.preventDefault();
        if (e.target.classList.contains('page-link') && !e.target.parentNode.classList.contains('disabled')) {
            const page = parseInt(e.target.dataset.page);
            if (page >= 1 && page <= totalPages) {
                showPageCallback(page);
            }
        }
    });
}

function updateTableInfo(tableId) {
    const tableInfo = document.getElementById(`${tableId}TableInfo`);
    if (!tableInfo) return;

    const table = document.getElementById(tableId);
    const visibleRows = table.querySelectorAll('tbody tr[style=""], tbody tr:not([style])');
    const totalRows = table.querySelectorAll('tbody tr').length;

    tableInfo.textContent = `Showing ${visibleRows.length} of ${totalRows} entries`;
}

// Cascading dropdowns for filters
document.addEventListener('DOMContentLoaded', function() {
    const countrySelect = document.getElementById('country_id');
    const provinceSelect = document.getElementById('province_id');
    const districtSelect = document.getElementById('district_id');
    const llgSelect = document.getElementById('llg_id');

    // Load initial data if filters are set
    if (countrySelect.value) {
        loadProvinces(countrySelect.value, '<?= $filters['province_id'] ?>');
    }

    countrySelect.addEventListener('change', function() {
        const countryId = this.value;
        provinceSelect.innerHTML = '<option value="">All Provinces</option>';
        districtSelect.innerHTML = '<option value="">All Districts</option>';
        llgSelect.innerHTML = '<option value="">All LLGs</option>';
        
        if (countryId) {
            loadProvinces(countryId);
        }
    });

    provinceSelect.addEventListener('change', function() {
        const provinceId = this.value;
        districtSelect.innerHTML = '<option value="">All Districts</option>';
        llgSelect.innerHTML = '<option value="">All LLGs</option>';
        
        if (provinceId) {
            loadDistricts(provinceId);
        }
    });

    districtSelect.addEventListener('change', function() {
        const districtId = this.value;
        llgSelect.innerHTML = '<option value="">All LLGs</option>';
        
        if (districtId) {
            loadLlgs(districtId);
        }
    });

    function loadProvinces(countryId, selectedId = '') {
        fetch(`<?= base_url('admin/locations/api/provinces') ?>/${countryId}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    data.data.forEach(province => {
                        const option = new Option(province.name, province.id);
                        if (province.id == selectedId) option.selected = true;
                        provinceSelect.add(option);
                    });

                    if (selectedId && provinceSelect.value) {
                        loadDistricts(provinceSelect.value, '<?= $filters['district_id'] ?>');
                    }
                } else {
                    console.error('Error loading provinces:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading provinces:', error);
            });
    }

    function loadDistricts(provinceId, selectedId = '') {
        fetch(`<?= base_url('admin/locations/api/districts') ?>/${provinceId}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    data.data.forEach(district => {
                        const option = new Option(district.name, district.id);
                        if (district.id == selectedId) option.selected = true;
                        districtSelect.add(option);
                    });

                    if (selectedId && districtSelect.value) {
                        loadLlgs(districtSelect.value, '<?= $filters['llg_id'] ?>');
                    }
                } else {
                    console.error('Error loading districts:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading districts:', error);
            });
    }

    function loadLlgs(districtId, selectedId = '') {
        fetch(`<?= base_url('admin/locations/api/llgs') ?>/${districtId}`, {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    data.data.forEach(llg => {
                        const option = new Option(llg.name, llg.id);
                        if (llg.id == selectedId) option.selected = true;
                        llgSelect.add(option);
                    });
                } else {
                    console.error('Error loading LLGs:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading LLGs:', error);
            });
    }
});
</script>
<?= $this->endSection() ?>
