<?php

namespace App\Controllers;

use App\Models\LlgModel;
use App\Models\CountryModel;
use App\Models\ProvinceModel;
use App\Models\DistrictModel;

class LlgsController extends BaseController
{
    protected $llgModel;
    protected $countryModel;
    protected $provinceModel;
    protected $districtModel;
    protected $data = [];

    public function __construct()
    {
        $this->llgModel = new LlgModel();
        $this->countryModel = new CountryModel();
        $this->provinceModel = new ProvinceModel();
        $this->districtModel = new DistrictModel();
    }

    /**
     * Display list of LLGs (GET)
     */
    public function index()
    {
        $search = $this->request->getGet('search') ?? '';
        $districtId = $this->request->getGet('district_id');
        $perPage = 20;
        
        // Get LLGs with hierarchy information
        $llgs = $this->llgModel->getLlgsWithHierarchy($perPage, $search, $districtId);
        $pager = $this->llgModel->pager;
        
        // Get hierarchy data for filter dropdowns
        $countries = $this->countryModel->getForDropdown();
        $provinces = $this->provinceModel->getForDropdown();
        $districts = $this->districtModel->getForDropdown();

        $this->data['title'] = 'LLGs Management - DCBuyer Admin';
        $this->data['active_menu'] = 'llgs';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'LLGs', 'url' => base_url('admin/llgs')]
        ];
        
        $this->data['page_title'] = 'LLGs Management';
        $this->data['page_description'] = 'Manage Local Level Governments in the location hierarchy.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/llgs/create') . '" class="btn btn-admin-primary">
                <i class="fas fa-plus me-2"></i>Add New LLG
            </a>
        ';
        
        $this->data['llgs'] = $llgs;
        $this->data['pager'] = $pager;
        $this->data['search'] = $search;
        $this->data['countries'] = $countries;
        $this->data['provinces'] = $provinces;
        $this->data['districts'] = $districts;
        $this->data['selected_district'] = $districtId;
        
        return view('admin/llgs/llgs_index', $this->data);
    }

    /**
     * Show create LLG form (GET)
     */
    public function create()
    {
        $this->data['title'] = 'Create LLG - DCBuyer Admin';
        $this->data['active_menu'] = 'llgs';

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'LLGs', 'url' => base_url('admin/llgs')],
            ['title' => 'Create LLG', 'url' => base_url('admin/llgs/create')]
        ];

        $this->data['page_title'] = 'Create New LLG';
        $this->data['page_description'] = 'Add a new Local Level Government to the location hierarchy.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/llgs') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to LLGs
            </a>
        ';

        // Get hierarchy data for dropdowns
        $this->data['countries'] = $this->countryModel->getForDropdown();
        $this->data['provinces'] = [];
        $this->data['districts'] = [];

        return view('admin/llgs/llgs_create', $this->data);
    }

    /**
     * Store new LLG (POST)
     */
    public function store()
    {
        $rules = [
            'district_id' => 'required|integer|is_not_unique[districts.id]',
            'name' => 'required|min_length[2]|max_length[100]',
            'code' => 'permit_empty|max_length[10]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'district_id' => $this->request->getPost('district_id'),
            'name' => $this->request->getPost('name'),
            'code' => $this->request->getPost('code') ?: null
        ];
        
        try {
            $result = $this->llgModel->createLlg($data);
            
            if ($result) {
                return redirect()->to('admin/llgs')->with('success', 'LLG created successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to create LLG.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating LLG: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while creating the LLG.');
        }
    }

    /**
     * View LLG details (GET)
     */
    public function show($id)
    {
        $llg = $this->llgModel->getLlgWithHierarchy($id);

        if (!$llg) {
            return redirect()->to('admin/llgs')->with('error', 'LLG not found.');
        }

        $this->data['llg'] = $llg;
        $this->data['title'] = 'LLG Details - DCBuyer Admin';
        $this->data['active_menu'] = 'llgs';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'LLGs', 'url' => base_url('admin/llgs')],
            ['title' => 'LLG Details', 'url' => base_url('admin/llgs/' . $id)]
        ];
        
        $this->data['page_title'] = 'LLG Details: ' . $llg['name'];
        $this->data['page_description'] = 'View detailed information about this Local Level Government.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/llgs/' . $id . '/edit') . '" class="btn btn-admin-primary">
                <i class="fas fa-edit me-2"></i>Edit LLG
            </a>
            <a href="' . base_url('admin/llgs') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to LLGs
            </a>
        ';
        
        return view('admin/llgs/llgs_show', $this->data);
    }

    /**
     * Show edit LLG form (GET)
     */
    public function edit($id)
    {
        $llg = $this->llgModel->getLlgWithHierarchy($id);

        if (!$llg) {
            return redirect()->to('admin/llgs')->with('error', 'LLG not found.');
        }

        $this->data['llg'] = $llg;
        $this->data['title'] = 'Edit LLG - DCBuyer Admin';
        $this->data['active_menu'] = 'llgs';
        
        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('admin/dashboard')],
            ['title' => 'LLGs', 'url' => base_url('admin/llgs')],
            ['title' => 'Edit LLG', 'url' => base_url('admin/llgs/' . $id . '/edit')]
        ];
        
        $this->data['page_title'] = 'Edit LLG: ' . $llg['name'];
        $this->data['page_description'] = 'Update Local Level Government information.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/llgs/' . $id) . '" class="btn btn-secondary">
                <i class="fas fa-eye me-2"></i>View Details
            </a>
            <a href="' . base_url('admin/llgs') . '" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to LLGs
            </a>
        ';

        // Get hierarchy data for dropdowns
        $this->data['countries'] = $this->countryModel->getForDropdown();
        $this->data['provinces'] = $this->provinceModel->getByCountryForDropdown($llg['country_id']);
        $this->data['districts'] = $this->districtModel->getByProvinceForDropdown($llg['province_id']);

        return view('admin/llgs/llgs_edit', $this->data);
    }

    /**
     * Update LLG (PUT/PATCH)
     */
    public function update($id)
    {
        $llg = $this->llgModel->find($id);

        if (!$llg) {
            return redirect()->to('admin/llgs')->with('error', 'LLG not found.');
        }
        
        $rules = [
            'district_id' => 'required|integer|is_not_unique[districts.id]',
            'name' => 'required|min_length[2]|max_length[100]',
            'code' => 'permit_empty|max_length[10]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'district_id' => $this->request->getPost('district_id'),
            'name' => $this->request->getPost('name'),
            'code' => $this->request->getPost('code') ?: null
        ];
        
        try {
            $result = $this->llgModel->updateLlg($id, $data);
            
            if ($result) {
                return redirect()->to('admin/llgs/' . $id)->with('success', 'LLG updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update LLG.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error updating LLG: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating the LLG.');
        }
    }

    /**
     * Delete LLG (DELETE)
     */
    public function delete($id)
    {
        $llg = $this->llgModel->find($id);

        if (!$llg) {
            return redirect()->to('admin/llgs')->with('error', 'LLG not found.');
        }

        try {
            // Check if LLG has locations
            if ($this->llgModel->hasLocations($id)) {
                return redirect()->to('admin/llgs')->with('error', 'Cannot delete LLG that has locations. Please delete all locations first.');
            }
            
            $result = $this->llgModel->delete($id);
            
            if ($result) {
                return redirect()->to('admin/llgs')->with('success', 'LLG deleted successfully.');
            } else {
                return redirect()->to('admin/llgs')->with('error', 'Failed to delete LLG.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error deleting LLG: ' . $e->getMessage());
            return redirect()->to('admin/llgs')->with('error', 'An error occurred while deleting the LLG.');
        }
    }


}
