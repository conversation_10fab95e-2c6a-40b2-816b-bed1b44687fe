<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .form-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .form-section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .form-control:focus {
        border-color: #2E7D32;
        box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
    }
    
    .form-select:focus {
        border-color: #2E7D32;
        box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
    }
    
    .required {
        color: #dc3545;
    }
    
    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .btn-admin-primary {
        background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
        border: none;
        color: white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .btn-admin-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
        color: white;
    }
    
    .unit-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.5rem;
        margin-top: 0.5rem;
    }
    
    .unit-option {
        padding: 0.5rem;
        background: #f8f9fa;
        border-radius: 6px;
        font-size: 0.875rem;
        color: #495057;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <form method="POST" action="<?= base_url('admin/commodities/store') ?>">
        <?= csrf_field() ?>
        
        <!-- Basic Information Section -->
        <div class="form-section">
            <h4 class="form-section-title">
                <i class="fas fa-seedling me-2 text-success"></i>
                Basic Information
            </h4>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="commodity_name" class="form-label">
                            Commodity Name <span class="required">*</span>
                        </label>
                        <input type="text" 
                               class="form-control <?= isset($errors['commodity_name']) ? 'is-invalid' : '' ?>" 
                               id="commodity_name" 
                               name="commodity_name" 
                               value="<?= old('commodity_name') ?>"
                               placeholder="Enter commodity name"
                               required>
                        <?php if (isset($errors['commodity_name'])): ?>
                            <div class="invalid-feedback"><?= $errors['commodity_name'] ?></div>
                        <?php endif; ?>
                        <div class="form-text">Enter a descriptive name for the commodity</div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="unit_of_measurement" class="form-label">
                            Unit of Measurement <span class="required">*</span>
                        </label>
                        <select class="form-select <?= isset($errors['unit_of_measurement']) ? 'is-invalid' : '' ?>" 
                                id="unit_of_measurement" 
                                name="unit_of_measurement" 
                                required>
                            <option value="">Select unit of measurement</option>
                            <?php foreach ($units as $value => $label): ?>
                                <option value="<?= $value ?>" <?= old('unit_of_measurement') === $value ? 'selected' : '' ?>>
                                    <?= $label ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($errors['unit_of_measurement'])): ?>
                            <div class="invalid-feedback"><?= $errors['unit_of_measurement'] ?></div>
                        <?php endif; ?>
                        <div class="form-text">Choose the standard unit for measuring this commodity</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information Section -->
        <div class="form-section">
            <h4 class="form-section-title">
                <i class="fas fa-info-circle me-2 text-info"></i>
                Additional Information
            </h4>
            
            <div class="form-group">
                <label for="remarks" class="form-label">Remarks</label>
                <textarea class="form-control <?= isset($errors['remarks']) ? 'is-invalid' : '' ?>" 
                          id="remarks" 
                          name="remarks" 
                          rows="4"
                          placeholder="Enter any additional notes or specifications about this commodity"><?= old('remarks') ?></textarea>
                <?php if (isset($errors['remarks'])): ?>
                    <div class="invalid-feedback"><?= $errors['remarks'] ?></div>
                <?php endif; ?>
                <div class="form-text">Optional: Add any special notes, specifications, or descriptions</div>
            </div>
        </div>

        <!-- Available Units Reference -->
        <div class="form-section">
            <h4 class="form-section-title">
                <i class="fas fa-ruler me-2 text-warning"></i>
                Available Units Reference
            </h4>
            
            <div class="unit-grid">
                <?php foreach ($units as $value => $label): ?>
                    <div class="unit-option">
                        <strong><?= strtoupper($value) ?></strong> - <?= $label ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="form-section">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <span class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Fields marked with <span class="required">*</span> are required
                    </span>
                </div>
                <div>
                    <a href="<?= base_url('admin/commodities') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-admin-primary">
                        <i class="fas fa-save me-2"></i>Create Commodity
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on commodity name field
    document.getElementById('commodity_name').focus();
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const commodityName = document.getElementById('commodity_name').value.trim();
        const unitOfMeasurement = document.getElementById('unit_of_measurement').value;
        
        if (!commodityName) {
            e.preventDefault();
            alert('Please enter a commodity name.');
            document.getElementById('commodity_name').focus();
            return;
        }
        
        if (!unitOfMeasurement) {
            e.preventDefault();
            alert('Please select a unit of measurement.');
            document.getElementById('unit_of_measurement').focus();
            return;
        }
    });
});
</script>
<?= $this->endSection() ?>
