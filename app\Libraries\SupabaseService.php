<?php

namespace App\Libraries;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class SupabaseService
{
    private $client;
    private $baseUrl;
    private $apiKey;
    private $headers;

    public function __construct()
    {
        $this->baseUrl = env('supabase.url');
        $this->apiKey = env('supabase.key');
        
        $this->client = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => 30,
        ]);
        
        $this->headers = [
            'apikey' => $this->apiKey,
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
            'Prefer' => 'return=representation'
        ];
    }

    /**
     * Select data from a table
     */
    public function select(string $table, array $columns = ['*'], array $filters = [], array $options = [])
    {
        try {
            $url = "/rest/v1/{$table}";
            $query = [];
            
            // Add column selection
            if (!empty($columns) && $columns !== ['*']) {
                $query['select'] = implode(',', $columns);
            }
            
            // Add filters
            foreach ($filters as $column => $value) {
                if (is_array($value)) {
                    // Handle operators like ['eq', 'value'] or ['in', ['val1', 'val2']]
                    $operator = $value[0];
                    $filterValue = $value[1];
                    
                    if ($operator === 'in' && is_array($filterValue)) {
                        $query[$column] = 'in.(' . implode(',', $filterValue) . ')';
                    } else {
                        $query[$column] = $operator . '.' . $filterValue;
                    }
                } else {
                    // Simple equality filter
                    $query[$column] = 'eq.' . $value;
                }
            }
            
            // Add options (limit, order, etc.)
            foreach ($options as $key => $value) {
                $query[$key] = $value;
            }
            
            $response = $this->client->get($url, [
                'headers' => $this->headers,
                'query' => $query
            ]);
            
            return json_decode($response->getBody()->getContents(), true);
            
        } catch (RequestException $e) {
            log_message('error', 'Supabase select error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Insert data into a table
     */
    public function insert(string $table, array $data)
    {
        try {
            $url = "/rest/v1/{$table}";
            
            $response = $this->client->post($url, [
                'headers' => $this->headers,
                'json' => $data
            ]);
            
            return json_decode($response->getBody()->getContents(), true);
            
        } catch (RequestException $e) {
            log_message('error', 'Supabase insert error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Update data in a table
     */
    public function update(string $table, array $data, array $filters)
    {
        try {
            $url = "/rest/v1/{$table}";
            $query = [];
            
            // Add filters
            foreach ($filters as $column => $value) {
                $query[$column] = 'eq.' . $value;
            }
            
            $response = $this->client->patch($url, [
                'headers' => $this->headers,
                'json' => $data,
                'query' => $query
            ]);
            
            return json_decode($response->getBody()->getContents(), true);
            
        } catch (RequestException $e) {
            log_message('error', 'Supabase update error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete data from a table
     */
    public function delete(string $table, array $filters)
    {
        try {
            $url = "/rest/v1/{$table}";
            $query = [];
            
            // Add filters
            foreach ($filters as $column => $value) {
                $query[$column] = 'eq.' . $value;
            }
            
            $response = $this->client->delete($url, [
                'headers' => $this->headers,
                'query' => $query
            ]);
            
            return json_decode($response->getBody()->getContents(), true);
            
        } catch (RequestException $e) {
            log_message('error', 'Supabase delete error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Authenticate user with email and password
     */
    public function signInWithPassword(string $email, string $password)
    {
        try {
            $url = "/auth/v1/token?grant_type=password";
            
            $response = $this->client->post($url, [
                'headers' => [
                    'apikey' => $this->apiKey,
                    'Content-Type' => 'application/json'
                ],
                'json' => [
                    'email' => $email,
                    'password' => $password
                ]
            ]);
            
            return json_decode($response->getBody()->getContents(), true);
            
        } catch (RequestException $e) {
            log_message('error', 'Supabase auth error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user information from access token
     */
    public function getUser(string $accessToken)
    {
        try {
            $url = "/auth/v1/user";
            
            $response = $this->client->get($url, [
                'headers' => [
                    'apikey' => $this->apiKey,
                    'Authorization' => 'Bearer ' . $accessToken
                ]
            ]);
            
            return json_decode($response->getBody()->getContents(), true);
            
        } catch (RequestException $e) {
            log_message('error', 'Supabase get user error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Execute a custom RPC function
     */
    public function rpc(string $functionName, array $params = [])
    {
        try {
            $url = "/rest/v1/rpc/{$functionName}";
            
            $response = $this->client->post($url, [
                'headers' => $this->headers,
                'json' => $params
            ]);
            
            return json_decode($response->getBody()->getContents(), true);
            
        } catch (RequestException $e) {
            log_message('error', 'Supabase RPC error: ' . $e->getMessage());
            return false;
        }
    }
}
