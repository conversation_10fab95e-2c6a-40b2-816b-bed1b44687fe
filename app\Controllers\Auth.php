<?php

namespace App\Controllers;

use App\Models\UserModel;

class Auth extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    public function login()
    {
        // Handle login form submission
        if ($this->request->getMethod() === 'POST') {
            $email = $this->request->getPost('email');
            $password = $this->request->getPost('password');

            // Simple validation
            if (empty($email) || empty($password)) {
                return redirect()->back()->withInput()->with('error', 'Email and password are required.');
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return redirect()->back()->withInput()->with('error', 'Please enter a valid email address.');
            }

            // Find and authenticate user
            $user = $this->userModel->findByEmail($email);

            if ($user && $user['status'] === 'active') {
                // Verify password
                if ($this->userModel->verifyPassword($password, $user['password_hash'])) {
                    // Set session data
                    session()->set([
                        'user_id' => $user['id'],
                        'email' => $user['email'],
                        'fullname' => $user['fullname'],
                        'username' => $user['username'],
                        'is_admin' => $user['is_admin'],
                        'is_buyer' => $user['is_buyer'],
                        'is_supervisor' => $user['is_supervisor'],
                        'logged_in' => true
                    ]);

                    return redirect()->to('/dashboard')->with('success', 'Login successful!');
                } else {
                    return redirect()->back()->withInput()->with('error', 'Invalid email or password.');
                }
            } else {
                $message = $user && $user['status'] !== 'active'
                    ? 'Your account is not active. Please contact administrator.'
                    : 'Invalid email or password.';

                return redirect()->back()->withInput()->with('error', $message);
            }
        }

        // If GET request, redirect to home page
        return redirect()->to('/');
    }
    
    public function logout()
    {
        // Destroy session
        session()->destroy();

        return redirect()->to('/')->with('success', 'You have been logged out successfully.');
    }
}
