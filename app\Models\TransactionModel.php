<?php

namespace App\Models;

use CodeIgniter\Model;

class TransactionModel extends Model
{
    protected $table = 'transactions';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'transaction_code',
        'user_id',
        'mission_id',
        'commodity_id',
        'customer_id',
        'location_id',
        'quantity',
        'unit_price',
        'payment_amount',
        'transaction_date',
        'remarks',
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'transaction_code' => 'required|max_length[30]|is_unique[transactions.transaction_code,id,{id}]',
        'user_id' => 'required|integer',
        'mission_id' => 'required|integer',
        'commodity_id' => 'required|integer',
        'location_id' => 'required|integer',
        'quantity' => 'required|decimal|greater_than[0]',
        'unit_price' => 'required|decimal|greater_than[0]',
        'payment_amount' => 'required|decimal|greater_than[0]',
        'transaction_date' => 'required|valid_date',
        'status' => 'required|in_list[pending,completed,cancelled]'
    ];

    protected $validationMessages = [
        'transaction_code' => [
            'required' => 'Transaction code is required.',
            'is_unique' => 'Transaction code must be unique.'
        ],
        'user_id' => [
            'required' => 'User is required.',
            'integer' => 'User must be a valid selection.'
        ],
        'mission_id' => [
            'required' => 'Mission is required.',
            'integer' => 'Mission must be a valid selection.'
        ],
        'commodity_id' => [
            'required' => 'Commodity is required.',
            'integer' => 'Commodity must be a valid selection.'
        ],
        'location_id' => [
            'required' => 'Location is required.',
            'integer' => 'Location must be a valid selection.'
        ],
        'quantity' => [
            'required' => 'Quantity is required.',
            'decimal' => 'Quantity must be a valid number.',
            'greater_than' => 'Quantity must be greater than 0.'
        ],
        'unit_price' => [
            'required' => 'Unit price is required.',
            'decimal' => 'Unit price must be a valid number.',
            'greater_than' => 'Unit price must be greater than 0.'
        ],
        'payment_amount' => [
            'required' => 'Payment amount is required.',
            'decimal' => 'Payment amount must be a valid number.',
            'greater_than' => 'Payment amount must be greater than 0.'
        ],
        'transaction_date' => [
            'required' => 'Transaction date is required.',
            'valid_date' => 'Transaction date must be a valid date.'
        ],
        'status' => [
            'required' => 'Status is required.',
            'in_list' => 'Status must be pending, completed, or cancelled.'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['generateTransactionCode'];
    protected $beforeUpdate = [];
    protected $afterInsert = ['updateMissionActualAmount'];
    protected $afterUpdate = ['updateMissionActualAmount'];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = ['updateMissionActualAmountOnDelete'];

    /**
     * Generate unique transaction code before insert
     */
    protected function generateTransactionCode(array $data)
    {
        if (!isset($data['data']['transaction_code']) || empty($data['data']['transaction_code'])) {
            $data['data']['transaction_code'] = $this->generateUniqueTransactionCode();
        }
        return $data;
    }

    /**
     * Generate unique transaction code
     */
    public function generateUniqueTransactionCode(): string
    {
        $year = date('Y');
        $month = date('m');
        
        // Get the last transaction code for this year/month
        $lastTransaction = $this->select('transaction_code')
            ->like('transaction_code', "TXN{$year}{$month}", 'after')
            ->orderBy('id', 'DESC')
            ->first();

        if ($lastTransaction) {
            // Extract the increment number and add 1
            $lastCode = $lastTransaction['transaction_code'];
            $increment = (int)substr($lastCode, -3) + 1;
        } else {
            $increment = 1;
        }

        return sprintf('TXN%s%s%03d', $year, $month, $increment);
    }

    /**
     * Update mission actual amount after transaction insert/update
     */
    protected function updateMissionActualAmount(array $data)
    {
        if (isset($data['data']['mission_id'])) {
            $this->recalculateMissionActualAmount($data['data']['mission_id']);
        }
        return $data;
    }

    /**
     * Update mission actual amount after transaction delete
     */
    protected function updateMissionActualAmountOnDelete(array $data)
    {
        if (isset($data['data']['mission_id'])) {
            $this->recalculateMissionActualAmount($data['data']['mission_id']);
        }
        return $data;
    }

    /**
     * Recalculate mission actual amount
     */
    protected function recalculateMissionActualAmount(int $missionId)
    {
        $totalAmount = $this->selectSum('payment_amount')
            ->where('mission_id', $missionId)
            ->where('status !=', 'cancelled')
            ->where('is_deleted', false)
            ->first()['payment_amount'] ?? 0;

        $missionModel = new \App\Models\MissionModel();
        $missionModel->update($missionId, ['actual_amount' => $totalAmount]);
    }

    /**
     * Get transactions with related data
     */
    public function getTransactionsWithDetails(int $perPage = 20, array $filters = []): array
    {
        $builder = $this->select('
            transactions.*,
            users.fullname as user_name,
            mission.mission_name,
            mission.mission_number,
            commodities.commodity_name,
            commodities.unit_of_measurement,
            customers.first_name as customer_first_name,
            customers.last_name as customer_last_name,
            customers.customer_code,
            locations.location_name,
            countries.name as country_name,
            provinces.name as province_name,
            districts.name as district_name
        ')
        ->join('users', 'users.id = transactions.user_id', 'left')
        ->join('mission', 'mission.id = transactions.mission_id', 'left')
        ->join('commodities', 'commodities.commodity_id = transactions.commodity_id', 'left')
        ->join('customers', 'customers.id = transactions.customer_id', 'left')
        ->join('locations', 'locations.id = transactions.location_id', 'left')
        ->join('countries', 'countries.id = locations.country_id', 'left')
        ->join('provinces', 'provinces.id = locations.province_id', 'left')
        ->join('districts', 'districts.id = locations.district_id', 'left');

        // Apply filters
        if (!empty($filters['search'])) {
            $builder->groupStart()
                ->like('transactions.transaction_code', $filters['search'])
                ->orLike('users.fullname', $filters['search'])
                ->orLike('mission.mission_name', $filters['search'])
                ->orLike('commodities.commodity_name', $filters['search'])
                ->orLike('customers.first_name', $filters['search'])
                ->orLike('customers.last_name', $filters['search'])
                ->groupEnd();
        }

        if (!empty($filters['status'])) {
            $builder->where('transactions.status', $filters['status']);
        }

        if (!empty($filters['user_id'])) {
            $builder->where('transactions.user_id', $filters['user_id']);
        }

        if (!empty($filters['mission_id'])) {
            $builder->where('transactions.mission_id', $filters['mission_id']);
        }

        if (!empty($filters['date_from'])) {
            $builder->where('transactions.transaction_date >=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $builder->where('transactions.transaction_date <=', $filters['date_to']);
        }

        return $builder->orderBy('transactions.created_at', 'DESC')
            ->paginate($perPage);
    }

    /**
     * Get transaction statistics
     */
    public function getTransactionStats(): array
    {
        $totalTransactions = $this->countAllResults();
        $pendingTransactions = $this->where('status', 'pending')->countAllResults();
        $completedTransactions = $this->where('status', 'completed')->countAllResults();
        $cancelledTransactions = $this->where('status', 'cancelled')->countAllResults();
        
        $totalAmount = $this->selectSum('payment_amount')
            ->where('status !=', 'cancelled')
            ->first()['payment_amount'] ?? 0;

        $recentTransactions = $this->where('created_at >=', date('Y-m-d H:i:s', strtotime('-30 days')))
            ->countAllResults();

        return [
            'total_transactions' => $totalTransactions,
            'pending_transactions' => $pendingTransactions,
            'completed_transactions' => $completedTransactions,
            'cancelled_transactions' => $cancelledTransactions,
            'total_amount' => $totalAmount,
            'recent_transactions' => $recentTransactions
        ];
    }

    /**
     * Get transactions for a specific user (buyer)
     */
    public function getUserTransactions(int $userId, int $perPage = 20): array
    {
        return $this->getTransactionsWithDetails($perPage, ['user_id' => $userId]);
    }

    /**
     * Get transactions for a specific mission
     */
    public function getMissionTransactions(int $missionId, int $perPage = 20): array
    {
        return $this->getTransactionsWithDetails($perPage, ['mission_id' => $missionId]);
    }

    /**
     * Create a new transaction
     */
    public function createTransaction(array $data, ?int $createdBy = null): bool
    {
        if ($createdBy) {
            $data['created_by'] = $createdBy;
            $data['updated_by'] = $createdBy;
        }

        // Validate that payment_amount equals quantity * unit_price
        if (isset($data['quantity'], $data['unit_price'], $data['payment_amount'])) {
            $calculatedAmount = round($data['quantity'] * $data['unit_price'], 2);
            if (abs($calculatedAmount - $data['payment_amount']) > 0.01) {
                $this->errors = ['payment_amount' => 'Payment amount must equal quantity × unit price.'];
                return false;
            }
        }

        return $this->insert($data);
    }

    /**
     * Validate mission assignment for user
     */
    public function validateMissionAssignment(int $missionId, int $userId): bool
    {
        $missionModel = new \App\Models\MissionModel();
        $mission = $missionModel->find($missionId);
        
        return $mission && $mission['user_id'] == $userId && 
               in_array($mission['mission_status'], ['pending', 'in_progress']);
    }

    /**
     * Validate commodity matches mission
     */
    public function validateCommodityMatch(int $missionId, int $commodityId): bool
    {
        $missionModel = new \App\Models\MissionModel();
        $mission = $missionModel->find($missionId);
        
        return $mission && $mission['commodity_id'] == $commodityId;
    }
}
