<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .detail-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .detail-section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }
    
    .detail-row {
        display: flex;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .detail-row:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        font-weight: 600;
        color: #495057;
        width: 200px;
        flex-shrink: 0;
    }
    
    .detail-value {
        color: #212529;
        flex-grow: 1;
    }
    
    .badge-country {
        background-color: #e3f2fd;
        color: #1976d2;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 500;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        text-align: center;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Display Success Message -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Country Details Section -->
    <div class="detail-section">
        <div class="detail-section-title">
            <i class="fas fa-globe me-2"></i>Country Information
        </div>
        
        <div class="detail-row">
            <div class="detail-label">Country Name:</div>
            <div class="detail-value">
                <strong><?= esc($country['name']) ?></strong>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">Country Code:</div>
            <div class="detail-value">
                <span class="badge-country"><?= esc($country['code']) ?></span>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">Created Date:</div>
            <div class="detail-value"><?= date('F j, Y \a\t g:i A', strtotime($country['created_at'])) ?></div>
        </div>
        
        <?php if (isset($country['updated_at']) && !empty($country['updated_at'])): ?>
        <div class="detail-row">
            <div class="detail-label">Last Updated:</div>
            <div class="detail-value"><?= date('F j, Y \a\t g:i A', strtotime($country['updated_at'])) ?></div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Statistics Section -->
    <div class="detail-section">
        <div class="detail-section-title">
            <i class="fas fa-chart-bar me-2"></i>Location Statistics
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?= $country['province_count'] ?? 0 ?></div>
                <div class="stat-label">Provinces</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $country['district_count'] ?? 0 ?></div>
                <div class="stat-label">Districts</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $country['llg_count'] ?? 0 ?></div>
                <div class="stat-label">LLGs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $country['location_count'] ?? 0 ?></div>
                <div class="stat-label">Locations</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="detail-section">
        <div class="detail-section-title">
            <i class="fas fa-tools me-2"></i>Quick Actions
        </div>
        
        <div class="row g-3">
            <div class="col-md-3">
                <a href="<?= base_url('admin/provinces?country_id=' . $country['id']) ?>" class="btn btn-outline-primary w-100">
                    <i class="fas fa-map me-2"></i>Manage Provinces
                </a>
            </div>
            <div class="col-md-3">
                <a href="<?= base_url('admin/countries/' . $country['id'] . '/edit') ?>" class="btn btn-outline-warning w-100">
                    <i class="fas fa-edit me-2"></i>Edit Country
                </a>
            </div>
            <div class="col-md-3">
                <a href="<?= base_url('admin/countries') ?>" class="btn btn-outline-secondary w-100">
                    <i class="fas fa-list me-2"></i>All Countries
                </a>
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-outline-danger w-100" onclick="confirmDelete()">
                    <i class="fas fa-trash me-2"></i>Delete Country
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the country "<strong><?= esc($country['name']) ?></strong>"?</p>
                <p class="text-danger"><small>This action cannot be undone and will also delete all related provinces, districts, LLGs, and locations.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="<?= base_url('admin/countries/' . $country['id']) ?>" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete Country</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function confirmDelete() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
<?= $this->endSection() ?>
