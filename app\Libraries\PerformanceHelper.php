<?php

namespace App\Libraries;

/**
 * Performance Helper Library
 * 
 * Provides utilities for monitoring and optimizing application performance
 */
class PerformanceHelper
{
    private static $queryStartTime;
    private static $queryCount = 0;
    
    /**
     * Start timing a database query
     */
    public static function startQueryTimer(): void
    {
        self::$queryStartTime = microtime(true);
        self::$queryCount++;
    }
    
    /**
     * End timing a database query and log if slow
     * 
     * @param string $queryDescription Description of the query
     * @param float $slowThreshold Threshold in seconds to consider slow
     */
    public static function endQueryTimer(string $queryDescription = '', float $slowThreshold = 0.5): void
    {
        if (self::$queryStartTime === null) {
            return;
        }
        
        $duration = microtime(true) - self::$queryStartTime;
        
        if ($duration > $slowThreshold) {
            log_message('warning', "Slow query detected: {$queryDescription} took {$duration}s");
        }
        
        if (ENVIRONMENT === 'development') {
            log_message('debug', "Query: {$queryDescription} took {$duration}s");
        }
        
        self::$queryStartTime = null;
    }
    
    /**
     * Get current query count for this request
     */
    public static function getQueryCount(): int
    {
        return self::$queryCount;
    }
    
    /**
     * Reset query count
     */
    public static function resetQueryCount(): void
    {
        self::$queryCount = 0;
    }
    
    /**
     * Check if caching is enabled and working
     */
    public static function isCacheWorking(): bool
    {
        try {
            $cache = \Config\Services::cache();
            $testKey = 'performance_test_' . time();
            $testValue = 'test_value';
            
            // Try to save and retrieve a test value
            $saved = $cache->save($testKey, $testValue, 60);
            if (!$saved) {
                return false;
            }
            
            $retrieved = $cache->get($testKey);
            $cache->delete($testKey);
            
            return $retrieved === $testValue;
        } catch (\Exception $e) {
            log_message('error', 'Cache test failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get memory usage information
     */
    public static function getMemoryUsage(): array
    {
        return [
            'current' => memory_get_usage(true),
            'current_formatted' => self::formatBytes(memory_get_usage(true)),
            'peak' => memory_get_peak_usage(true),
            'peak_formatted' => self::formatBytes(memory_get_peak_usage(true)),
            'limit' => ini_get('memory_limit')
        ];
    }
    
    /**
     * Format bytes into human readable format
     */
    private static function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Get database connection information
     */
    public static function getDatabaseInfo(): array
    {
        try {
            $db = \Config\Database::connect();
            
            return [
                'driver' => $db->DBDriver,
                'hostname' => $db->hostname,
                'database' => $db->database,
                'persistent' => $db->pConnect,
                'debug' => $db->DBDebug,
                'connected' => $db->connID !== false
            ];
        } catch (\Exception $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate performance report
     */
    public static function generateReport(): array
    {
        return [
            'timestamp' => date('Y-m-d H:i:s'),
            'environment' => ENVIRONMENT,
            'memory' => self::getMemoryUsage(),
            'database' => self::getDatabaseInfo(),
            'cache_working' => self::isCacheWorking(),
            'query_count' => self::getQueryCount(),
            'php_version' => PHP_VERSION,
            'ci_version' => \CodeIgniter\CodeIgniter::CI_VERSION
        ];
    }
    
    /**
     * Log performance metrics
     */
    public static function logPerformanceMetrics(): void
    {
        if (ENVIRONMENT === 'development') {
            $report = self::generateReport();
            log_message('info', 'Performance Report: ' . json_encode($report));
        }
    }
    
    /**
     * Check if current request is slow
     * 
     * @param float $threshold Threshold in seconds
     */
    public static function isSlowRequest(float $threshold = 2.0): bool
    {
        if (!defined('APP_START_TIME')) {
            return false;
        }
        
        $duration = microtime(true) - APP_START_TIME;
        return $duration > $threshold;
    }
    
    /**
     * Get request duration
     */
    public static function getRequestDuration(): float
    {
        if (!defined('APP_START_TIME')) {
            return 0.0;
        }
        
        return microtime(true) - APP_START_TIME;
    }
    
    /**
     * Optimize database connection for current environment
     */
    public static function optimizeDatabaseConnection(): void
    {
        if (ENVIRONMENT === 'production') {
            // Enable query result caching in production
            $db = \Config\Database::connect();
            
            // Set connection timeout
            if ($db->DBDriver === 'Postgre') {
                $db->query("SET statement_timeout = '30s'");
                $db->query("SET lock_timeout = '10s'");
            }
        }
    }
}
