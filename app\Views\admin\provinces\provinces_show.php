<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .detail-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .detail-section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f1f3f4;
    }
    
    .detail-row {
        display: flex;
        margin-bottom: 1rem;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .detail-row:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .detail-label {
        font-weight: 600;
        color: #495057;
        min-width: 150px;
        flex-shrink: 0;
    }
    
    .detail-value {
        color: #6c757d;
        flex-grow: 1;
    }
    
    .badge-country {
        background-color: #e3f2fd;
        color: #1976d2;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .info-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .info-box h6 {
        color: #155724;
        margin-bottom: 0.5rem;
    }
    
    .info-box p {
        margin-bottom: 0;
        font-size: 0.9rem;
        color: #155724;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        font-size: 0.875rem;
        opacity: 0.9;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Information Box -->
    <div class="info-box">
        <h6><i class="fas fa-info-circle me-2"></i>Province Details</h6>
        <p>View detailed information about this province and its position in the location hierarchy.</p>
    </div>
    
    <!-- Province Information Section -->
    <div class="detail-section">
        <h5 class="detail-section-title">
            <i class="fas fa-map me-2"></i>Province Information
        </h5>
        
        <div class="detail-row">
            <div class="detail-label">Province Name:</div>
            <div class="detail-value">
                <strong><?= esc($province['name']) ?></strong>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">Country:</div>
            <div class="detail-value">
                <span class="badge-country"><?= esc($province['country_name']) ?></span>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">Province Code:</div>
            <div class="detail-value">
                <?= $province['code'] ? esc($province['code']) : '<em class="text-muted">Not specified</em>' ?>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">Created Date:</div>
            <div class="detail-value">
                <?= date('F j, Y \a\t g:i A', strtotime($province['created_at'])) ?>
            </div>
        </div>
        
        <?php if (!empty($province['updated_at'])): ?>
        <div class="detail-row">
            <div class="detail-label">Last Updated:</div>
            <div class="detail-value">
                <?= date('F j, Y \a\t g:i A', strtotime($province['updated_at'])) ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Statistics Section -->
    <div class="row">
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-number">
                    <i class="fas fa-map-marked-alt"></i>
                    <div>—</div>
                </div>
                <div class="stats-label">Districts</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-number">
                    <i class="fas fa-building"></i>
                    <div>—</div>
                </div>
                <div class="stats-label">LLGs</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-number">
                    <i class="fas fa-map-pin"></i>
                    <div>—</div>
                </div>
                <div class="stats-label">Locations</div>
            </div>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="detail-section">
        <div class="d-flex justify-content-between">
            <a href="<?= base_url('admin/provinces') ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Provinces
            </a>
            <div>
                <a href="<?= base_url('admin/provinces/' . $province['id'] . '/edit') ?>" class="btn btn-admin-primary me-2">
                    <i class="fas fa-edit me-2"></i>Edit Province
                </a>
                <button type="button" class="btn btn-outline-danger" onclick="confirmDelete()">
                    <i class="fas fa-trash me-2"></i>Delete Province
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the province "<strong><?= esc($province['name']) ?></strong>"?</p>
                <p class="text-danger"><small>This action cannot be undone and will also delete all related districts, LLGs, and locations.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="<?= base_url('admin/provinces/' . $province['id']) ?>" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <input type="hidden" name="_method" value="DELETE">
                    <button type="submit" class="btn btn-danger">Delete Province</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function confirmDelete() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
<?= $this->endSection() ?>
