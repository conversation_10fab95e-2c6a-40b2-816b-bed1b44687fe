<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= esc($title) ?></title>
    <link rel="icon" type="image/x-icon" href="<?= base_url('public/assets/images/dcb_icon_favicon.ico') ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        
        .setup-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 2rem;
            margin-bottom: 2rem;
            padding: 2rem;
        }
        
        .btn-custom {
            background: #2E7D32;
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            background: #4CAF50;
            transform: translateY(-2px);
            color: white;
        }
        
        .status-box {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #2E7D32;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="setup-container">
                    <div class="text-center mb-4">
                        <img src="<?= base_url('public/assets/images/dcb_logo.png') ?>" alt="DCB Logo" style="max-width: 200px;">
                        <h1 class="mt-3" style="color: #1A365D;">DCBuyer Setup</h1>
                        <p class="text-muted">Initialize your Supabase database and create test users</p>
                    </div>

                    <div class="status-box">
                        <h5><i class="fas fa-info-circle me-2"></i>Current Configuration</h5>
                        <p><strong>Environment:</strong> <?= esc($environment) ?></p>
                        <p><strong>Supabase URL:</strong> <?= esc($supabase_url) ?></p>
                        <p class="mb-0"><strong>Status:</strong> <span id="connection-status">Not tested</span></p>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-plug fa-3x text-primary mb-3"></i>
                                    <h5 class="card-title">Test Connection</h5>
                                    <p class="card-text">Verify that the application can connect to Supabase</p>
                                    <button class="btn btn-custom" onclick="testConnection()">
                                        <i class="fas fa-wifi me-2"></i>Test Connection
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-3x text-success mb-3"></i>
                                    <h5 class="card-title">Create Test Users</h5>
                                    <p class="card-text">Create initial admin, buyer, and seller accounts</p>
                                    <button class="btn btn-custom" onclick="createUsers()">
                                        <i class="fas fa-user-plus me-2"></i>Create Users
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-key me-2"></i>Test Credentials</h6>
                        <p class="mb-1"><strong>Admin:</strong> <EMAIL> / password123</p>
                        <p class="mb-1"><strong>Buyer:</strong> <EMAIL> / password123</p>
                        <p class="mb-0"><strong>Seller:</strong> <EMAIL> / password123</p>
                    </div>

                    <div id="result-container" class="mt-4" style="display: none;">
                        <div id="result-content"></div>
                    </div>

                    <div class="text-center mt-4">
                        <a href="<?= base_url('/') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i>Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testConnection() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Testing...';
            button.disabled = true;

            fetch('<?= base_url('setup/testConnection') ?>')
                .then(response => response.json())
                .then(data => {
                    const statusElement = document.getElementById('connection-status');
                    const resultContainer = document.getElementById('result-container');
                    const resultContent = document.getElementById('result-content');

                    if (data.status === 'success') {
                        statusElement.innerHTML = '<span class="text-success"><i class="fas fa-check-circle me-1"></i>Connected</span>';
                        resultContent.innerHTML = `
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle me-2"></i>Connection Successful</h6>
                                <p class="mb-0">${data.message}</p>
                            </div>
                        `;
                    } else {
                        statusElement.innerHTML = '<span class="text-danger"><i class="fas fa-times-circle me-1"></i>Failed</span>';
                        resultContent.innerHTML = `
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Connection Failed</h6>
                                <p class="mb-0">${data.message}</p>
                            </div>
                        `;
                    }

                    resultContainer.style.display = 'block';
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('connection-status').innerHTML = '<span class="text-danger"><i class="fas fa-times-circle me-1"></i>Error</span>';
                })
                .finally(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
        }

        function createUsers() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
            button.disabled = true;

            fetch('<?= base_url('setup/createInitialData') ?>')
                .then(response => response.json())
                .then(data => {
                    const resultContainer = document.getElementById('result-container');
                    const resultContent = document.getElementById('result-content');

                    if (data.status === 'success') {
                        resultContent.innerHTML = `
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle me-2"></i>Setup Completed</h6>
                                <p>${data.message}</p>
                                ${data.details ? '<ul class="mb-0">' + data.details.map(detail => `<li>${detail}</li>`).join('') + '</ul>' : ''}
                            </div>
                        `;
                    } else {
                        resultContent.innerHTML = `
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Setup Failed</h6>
                                <p class="mb-0">${data.message}</p>
                            </div>
                        `;
                    }

                    resultContainer.style.display = 'block';
                })
                .catch(error => {
                    console.error('Error:', error);
                    const resultContainer = document.getElementById('result-container');
                    const resultContent = document.getElementById('result-content');
                    resultContent.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Error</h6>
                            <p class="mb-0">An error occurred while creating users.</p>
                        </div>
                    `;
                    resultContainer.style.display = 'block';
                })
                .finally(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
        }
    </script>
</body>
</html>
