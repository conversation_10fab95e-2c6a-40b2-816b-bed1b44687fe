<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .detail-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .detail-header {
        border-bottom: 2px solid #f1f3f4;
        padding-bottom: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .detail-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2E7D32;
        margin-bottom: 0.5rem;
    }
    
    .detail-subtitle {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .detail-row:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        font-weight: 600;
        color: #495057;
        flex: 0 0 200px;
    }
    
    .detail-value {
        flex: 1;
        text-align: right;
    }
    
    .unit-badge {
        background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .status-badge {
        background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .audit-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
    }
    
    .audit-info h6 {
        color: #495057;
        margin-bottom: 0.75rem;
        font-weight: 600;
    }
    
    .audit-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }
    
    .audit-item:last-child {
        margin-bottom: 0;
    }
    
    .audit-label {
        color: #6c757d;
        font-weight: 500;
    }
    
    .audit-value {
        color: #495057;
    }
    
    .remarks-section {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 1.5rem;
        margin-top: 1rem;
    }
    
    .remarks-section h6 {
        color: #856404;
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .remarks-text {
        color: #856404;
        line-height: 1.6;
        margin: 0;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Main Details Card -->
    <div class="detail-card">
        <div class="detail-header">
            <div class="detail-title">
                <i class="fas fa-seedling me-2"></i>
                <?= esc($commodity['commodity_name']) ?>
            </div>
            <div class="detail-subtitle">
                Commodity ID: #<?= $commodity['commodity_id'] ?>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">
                <i class="fas fa-tag me-2 text-primary"></i>
                Commodity Name
            </div>
            <div class="detail-value">
                <strong><?= esc($commodity['commodity_name']) ?></strong>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">
                <i class="fas fa-ruler me-2 text-info"></i>
                Unit of Measurement
            </div>
            <div class="detail-value">
                <span class="unit-badge">
                    <?= strtoupper(esc($commodity['unit_of_measurement'])) ?>
                    (<?= $units[$commodity['unit_of_measurement']] ?>)
                </span>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">
                <i class="fas fa-check-circle me-2 text-success"></i>
                Status
            </div>
            <div class="detail-value">
                <span class="status-badge">Active</span>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">
                <i class="fas fa-calendar-plus me-2 text-warning"></i>
                Created Date
            </div>
            <div class="detail-value">
                <?= date('F j, Y \a\t g:i A', strtotime($commodity['created_at'])) ?>
            </div>
        </div>
        
        <div class="detail-row">
            <div class="detail-label">
                <i class="fas fa-calendar-edit me-2 text-secondary"></i>
                Last Updated
            </div>
            <div class="detail-value">
                <?= date('F j, Y \a\t g:i A', strtotime($commodity['updated_at'])) ?>
            </div>
        </div>
    </div>

    <!-- Remarks Section -->
    <?php if (!empty($commodity['remarks'])): ?>
    <div class="detail-card">
        <div class="remarks-section">
            <h6>
                <i class="fas fa-sticky-note me-2"></i>
                Remarks & Notes
            </h6>
            <p class="remarks-text"><?= nl2br(esc($commodity['remarks'])) ?></p>
        </div>
    </div>
    <?php endif; ?>

    <!-- Audit Information -->
    <div class="detail-card">
        <h5 class="mb-3">
            <i class="fas fa-history me-2 text-muted"></i>
            Audit Information
        </h5>
        
        <div class="audit-info">
            <h6>Creation Details</h6>
            <div class="audit-item">
                <span class="audit-label">Created By:</span>
                <span class="audit-value">User ID #<?= $commodity['created_by'] ?></span>
            </div>
            <div class="audit-item">
                <span class="audit-label">Created At:</span>
                <span class="audit-value"><?= date('M j, Y g:i A', strtotime($commodity['created_at'])) ?></span>
            </div>
        </div>
        
        <div class="audit-info">
            <h6>Last Update Details</h6>
            <div class="audit-item">
                <span class="audit-label">Updated By:</span>
                <span class="audit-value">User ID #<?= $commodity['updated_by'] ?></span>
            </div>
            <div class="audit-item">
                <span class="audit-label">Updated At:</span>
                <span class="audit-value"><?= date('M j, Y g:i A', strtotime($commodity['updated_at'])) ?></span>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="detail-card">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <span class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Commodity details and specifications
                </span>
            </div>
            <div>
                <a href="<?= base_url('admin/commodities') ?>" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>Back to List
                </a>
                <a href="<?= base_url('admin/commodities/' . $commodity['commodity_id'] . '/edit') ?>" class="btn btn-admin-primary">
                    <i class="fas fa-edit me-2"></i>Edit Commodity
                </a>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any interactive features here if needed
    console.log('Commodity details page loaded');
});
</script>
<?= $this->endSection() ?>
