<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<style>
    .form-card {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    
    .form-header {
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }
    
    .form-section {
        margin-bottom: 2rem;
    }
    
    .form-section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #495057;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e9ecef;
    }
    
    .required {
        color: #dc3545;
    }
    
    .form-control:focus {
        border-color: #2E7D32;
        box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
    }
    
    .form-select:focus {
        border-color: #2E7D32;
        box-shadow: 0 0 0 0.2rem rgba(46, 125, 50, 0.25);
    }
    
    .form-control.is-invalid {
        border-color: #dc3545;
    }
    
    .form-control.is-valid {
        border-color: #2E7D32;
    }
    
    .btn-admin-primary {
        background: linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%);
        border: none;
        color: white;
        font-weight: 600;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .btn-admin-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
        color: white;
    }
    
    .preview-card {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        text-align: center;
    }
    
    .preview-content {
        color: #6c757d;
    }
    
    .customer-code-preview {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2E7D32;
        margin-bottom: 0.5rem;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <form method="POST" action="<?= base_url('admin/customers/store') ?>" id="customerForm">
                <?= csrf_field() ?>
                
                <div class="form-card">
                    <div class="form-header">
                        <h4 class="mb-0">Customer Information</h4>
                        <small class="text-muted">Enter the customer's basic information</small>
                    </div>

                    <!-- Validation Errors -->
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please correct the following errors:</h6>
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <!-- Personal Information Section -->
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-user me-2"></i>Personal Information
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">
                                        First Name <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" 
                                           value="<?= old('first_name', $customer['first_name']) ?>" 
                                           placeholder="Enter first name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">
                                        Last Name <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" 
                                           value="<?= old('last_name', $customer['last_name']) ?>" 
                                           placeholder="Enter last name" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information Section -->
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-phone me-2"></i>Contact Information
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?= old('phone', $customer['phone']) ?>" 
                                           placeholder="Enter phone number">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= old('email', $customer['email']) ?>" 
                                           placeholder="Enter email address">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="3" 
                                      placeholder="Enter full address"><?= old('address', $customer['address']) ?></textarea>
                        </div>
                    </div>

                    <!-- Location and Status Section -->
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-map-marker-alt me-2"></i>Location & Status
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="location_id" class="form-label">Location</label>
                                    <select class="form-select" id="location_id" name="location_id">
                                        <option value="">Select a location (optional)</option>
                                        <?php foreach ($locations as $location): ?>
                                            <option value="<?= $location['id'] ?>" 
                                                    <?= old('location_id', $customer['location_id']) == $location['id'] ? 'selected' : '' ?>>
                                                <?= esc($location['location_name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">Optional: Select the customer's primary location</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">
                                        Status <span class="required">*</span>
                                    </label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="">Select status</option>
                                        <option value="active" <?= old('status', $customer['status']) === 'active' ? 'selected' : '' ?>>
                                            Active
                                        </option>
                                        <option value="inactive" <?= old('status', $customer['status']) === 'inactive' ? 'selected' : '' ?>>
                                            Inactive
                                        </option>
                                    </select>
                                    <div class="form-text">Active customers can be used in transactions</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Code Preview -->
                    <div class="form-section">
                        <h5 class="form-section-title">
                            <i class="fas fa-id-card me-2"></i>Customer Code
                        </h5>
                        
                        <div class="preview-card">
                            <div class="preview-content">
                                <div class="customer-code-preview">CUST<?= date('Y') ?>XXX</div>
                                <small>Customer code will be auto-generated upon creation</small>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex gap-2 justify-content-end">
                                <a href="<?= base_url('admin/customers') ?>" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <button type="reset" class="btn btn-outline-secondary">
                                    <i class="fas fa-undo me-2"></i>Reset
                                </button>
                                <button type="submit" class="btn btn-admin-primary">
                                    <i class="fas fa-save me-2"></i>Create Customer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize form validation
    const form = document.getElementById('customerForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function(e) {
        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
        submitBtn.disabled = true;
    });

    // Auto-format phone number
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 0) {
            if (value.length <= 3) {
                value = value;
            } else if (value.length <= 6) {
                value = value.substring(0, 3) + '-' + value.substring(3);
            } else {
                value = value.substring(0, 3) + '-' + value.substring(3, 6) + '-' + value.substring(6, 10);
            }
        }
        e.target.value = value;
    });

    // Real-time validation feedback
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        field.addEventListener('blur', function() {
            validateField(this);
        });
        
        field.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                validateField(this);
            }
        });
    });

    function validateField(field) {
        const isValid = field.checkValidity();
        
        if (isValid) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
        }
    }

    // Email validation
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('blur', function() {
        const email = this.value.trim();
        if (email && !isValidEmail(email)) {
            this.classList.add('is-invalid');
            showFieldError(this, 'Please enter a valid email address');
        } else {
            this.classList.remove('is-invalid');
            hideFieldError(this);
        }
    });

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function showFieldError(field, message) {
        hideFieldError(field);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }

    function hideFieldError(field) {
        const existingError = field.parentNode.querySelector('.invalid-feedback');
        if (existingError) {
            existingError.remove();
        }
    }

    // Auto-generate customer code preview based on current year
    const currentYear = new Date().getFullYear();
    const codePreview = document.querySelector('.customer-code-preview');
    if (codePreview) {
        codePreview.textContent = `CUST${currentYear}XXX`;
    }
});
</script>
<?= $this->endSection() ?>