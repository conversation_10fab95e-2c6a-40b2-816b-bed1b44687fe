<?php

namespace App\Models;

use CodeIgniter\Model;

class CountryModel extends Model
{
    protected $table = 'countries';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'name',
        'code'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = '';

    // Validation
    protected $validationRules = [
        'name' => 'required|min_length[2]|max_length[100]',
        'code' => 'required|min_length[2]|max_length[3]|is_unique[countries.code,id,{id}]'
    ];

    protected $validationMessages = [
        'name' => [
            'required' => 'Country name is required.',
            'min_length' => 'Country name must be at least 2 characters long.',
            'max_length' => 'Country name cannot exceed 100 characters.'
        ],
        'code' => [
            'required' => 'Country code is required.',
            'min_length' => 'Country code must be at least 2 characters long.',
            'max_length' => 'Country code cannot exceed 3 characters.',
            'is_unique' => 'This country code already exists.'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get all countries for dropdown
     */
    public function getForDropdown(): array
    {
        return $this->select('id, name')
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Get country by code
     */
    public function findByCode(string $code): ?array
    {
        return $this->where('code', $code)->first();
    }

    /**
     * Get countries with pagination
     */
    public function getCountriesPaginated(int $perPage = 20, string $search = ''): array
    {
        $builder = $this->select('*');
        
        if (!empty($search)) {
            $builder->groupStart()
                    ->like('name', $search)
                    ->orLike('code', $search)
                    ->groupEnd();
        }
        
        return $builder->orderBy('name', 'ASC')
                      ->paginate($perPage);
    }

    /**
     * Get country statistics
     */
    public function getCountryStats(): array
    {
        return [
            'total_countries' => $this->countAllResults(),
            'recent_countries' => $this->where('created_at >=', date('Y-m-d H:i:s', strtotime('-30 days')))
                                      ->countAllResults()
        ];
    }

    /**
     * Create a new country
     */
    public function createCountry(array $data): bool
    {
        return $this->insert($data) !== false;
    }

    /**
     * Update country data
     */
    public function updateCountry(int $id, array $data): bool
    {
        return $this->update($id, $data);
    }

    /**
     * Check if country has provinces
     */
    public function hasProvinces(int $countryId): bool
    {
        $provinceModel = new \App\Models\ProvinceModel();
        return $provinceModel->where('country_id', $countryId)->countAllResults() > 0;
    }
}
