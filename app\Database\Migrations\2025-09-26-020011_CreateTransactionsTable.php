<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateTransactionsTable extends Migration
{
    public function up()
    {
        // Check if buy_transactions table exists and rename it to transactions
        if ($this->db->tableExists('buy_transactions')) {
            // Rename buy_transactions to transactions
            $this->db->query('ALTER TABLE buy_transactions RENAME TO transactions');

            // Rename indexes if they exist
            $indexes = [
                'idx_buy_transactions_code' => 'idx_transactions_code',
                'idx_buy_transactions_user_id' => 'idx_transactions_user_id',
                'idx_buy_transactions_mission_id' => 'idx_transactions_mission_id',
                'idx_buy_transactions_commodity_id' => 'idx_transactions_commodity_id',
                'idx_buy_transactions_customer_id' => 'idx_transactions_customer_id',
                'idx_buy_transactions_location_id' => 'idx_transactions_location_id',
                'idx_buy_transactions_date' => 'idx_transactions_date',
                'idx_buy_transactions_status' => 'idx_transactions_status',
                'idx_buy_transactions_created_at' => 'idx_transactions_created_at',
                'idx_buy_transactions_is_deleted' => 'idx_transactions_is_deleted'
            ];

            foreach ($indexes as $oldIndex => $newIndex) {
                try {
                    $this->db->query("ALTER INDEX {$oldIndex} RENAME TO {$newIndex}");
                } catch (\Exception $e) {
                    // Index might not exist, continue
                }
            }
        } else {
            // Create transactions table from scratch
            $this->forge->addField([
                'id' => [
                    'type' => 'SERIAL',
                ],
                'transaction_code' => [
                    'type' => 'VARCHAR',
                    'constraint' => 30,
                    'unique' => true,
                ],
                'user_id' => [
                    'type' => 'BIGINT',
                    'null' => false,
                ],
                'mission_id' => [
                    'type' => 'BIGINT',
                    'null' => false,
                ],
                'commodity_id' => [
                    'type' => 'BIGINT',
                    'null' => false,
                ],
                'customer_id' => [
                    'type' => 'BIGINT',
                    'null' => true,
                ],
                'location_id' => [
                    'type' => 'BIGINT',
                    'null' => false,
                ],
                'quantity' => [
                    'type' => 'DECIMAL',
                    'constraint' => '10,3',
                    'null' => false,
                ],
                'unit_price' => [
                    'type' => 'DECIMAL',
                    'constraint' => '10,2',
                    'null' => false,
                ],
                'payment_amount' => [
                    'type' => 'DECIMAL',
                    'constraint' => '15,2',
                    'null' => false,
                ],
                'transaction_date' => [
                    'type' => 'DATE',
                    'null' => false,
                ],
                'remarks' => [
                    'type' => 'TEXT',
                    'null' => true,
                ],
                'status' => [
                    'type' => 'VARCHAR',
                    'constraint' => 20,
                    'default' => 'pending',
                ],
                'created_at' => [
                    'type' => 'TIMESTAMP',
                    'null' => false,
                    'default' => 'NOW()',
                ],
                'created_by' => [
                    'type' => 'BIGINT',
                    'null' => true,
                ],
                'updated_at' => [
                    'type' => 'TIMESTAMP',
                    'null' => false,
                    'default' => 'NOW()',
                ],
                'updated_by' => [
                    'type' => 'BIGINT',
                    'null' => true,
                ],
                'deleted_at' => [
                    'type' => 'TIMESTAMP',
                    'null' => true,
                ],
                'deleted_by' => [
                    'type' => 'BIGINT',
                    'null' => true,
                ],
                'is_deleted' => [
                    'type' => 'BOOLEAN',
                    'default' => false,
                ],
            ]);

            $this->forge->addPrimaryKey('id');
            $this->forge->addUniqueKey('transaction_code');

            // Add indexes for performance
            $this->forge->addKey('user_id');
            $this->forge->addKey('mission_id');
            $this->forge->addKey('commodity_id');
            $this->forge->addKey('customer_id');
            $this->forge->addKey('location_id');
            $this->forge->addKey('transaction_date');
            $this->forge->addKey('status');
            $this->forge->addKey('created_at');
            $this->forge->addKey('is_deleted');

            $this->forge->createTable('transactions');

            // Add foreign key constraints
            $this->forge->addForeignKey('user_id', 'users', 'id', 'CASCADE', 'SET NULL');
            $this->forge->addForeignKey('mission_id', 'mission', 'id', 'CASCADE', 'SET NULL');
            $this->forge->addForeignKey('commodity_id', 'commodities', 'commodity_id', 'CASCADE', 'SET NULL');
            $this->forge->addForeignKey('location_id', 'locations', 'id', 'CASCADE', 'SET NULL');
        }
    }

    public function down()
    {
        // Drop the transactions table
        $this->forge->dropTable('transactions');
    }
}
