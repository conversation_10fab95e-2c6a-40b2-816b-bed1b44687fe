<?php

namespace App\Controllers;

use App\Models\TransactionModel;
use App\Models\UserModel;
use App\Models\MissionModel;
use App\Models\CommodityModel;
use App\Models\CustomerModel;
use App\Models\LocationModel;

class TransactionController extends BaseController
{
    protected $transactionModel;
    protected $userModel;
    protected $missionModel;
    protected $commodityModel;
    protected $customerModel;
    protected $locationModel;
    protected $data = [];

    public function __construct()
    {
        $this->transactionModel = new TransactionModel();
        $this->userModel = new UserModel();
        $this->missionModel = new MissionModel();
        $this->commodityModel = new CommodityModel();
        $this->customerModel = new CustomerModel();
        $this->locationModel = new LocationModel();

        // Load helpers
        helper(['form', 'url']);

        // Initialize common admin data
        $this->data['title'] = 'Transaction Management - DCBuyer Admin';
        $this->data['active_menu'] = 'transactions';
        $this->data['user_name'] = session('fullname') ?? session('username') ?? 'User';
        $this->data['user_email'] = session('email') ?? '<EMAIL>';
        $this->data['user_role'] = session('is_admin') ? 'administrator' : (session('is_supervisor') ? 'supervisor' : 'user');
    }

    /**
     * Display list of transactions (GET)
     */
    public function index()
    {
        $search = $this->request->getGet('search') ?? '';
        $status = $this->request->getGet('status') ?? '';
        $userId = $this->request->getGet('user_id') ?? '';
        $missionId = $this->request->getGet('mission_id') ?? '';
        $dateFrom = $this->request->getGet('date_from') ?? '';
        $dateTo = $this->request->getGet('date_to') ?? '';
        $perPage = 20;

        // Build filters array
        $filters = array_filter([
            'search' => $search,
            'status' => $status,
            'user_id' => $userId,
            'mission_id' => $missionId,
            'date_from' => $dateFrom,
            'date_to' => $dateTo
        ]);

        // Get transactions with pagination and filters
        $transactions = $this->transactionModel->getTransactionsWithDetails($perPage, $filters);
        $pager = $this->transactionModel->pager;

        // Get transaction statistics
        $stats = $this->transactionModel->getTransactionStats();

        // Get filter options
        $users = $this->userModel->where('is_buyer', true)
                                ->where('status', 'active')
                                ->orderBy('fullname', 'ASC')
                                ->findAll();

        $missions = $this->missionModel->select('id, mission_number, mission_name')
                                     ->where('mission_status !=', 'cancelled')
                                     ->orderBy('mission_date', 'DESC')
                                     ->findAll();

        $this->data['transactions'] = $transactions;
        $this->data['pager'] = $pager;
        $this->data['stats'] = $stats;
        $this->data['users'] = $users;
        $this->data['missions'] = $missions;
        $this->data['filters'] = $filters;

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Transactions', 'url' => base_url('admin/transactions')]
        ];

        // If mission_id is specified, add it to breadcrumbs and modify page description
        if ($missionId) {
            $mission = $this->missionModel->find($missionId);
            if ($mission) {
                $this->data['breadcrumbs'][] = ['title' => 'Mission: ' . $mission['mission_number'], 'url' => '#'];
                $this->data['page_description'] = 'Transactions for Mission: ' . $mission['mission_number'] . ' - ' . $mission['mission_name'];
            }
        }

        $this->data['page_title'] = 'Transaction Management';
        $this->data['page_description'] = 'Manage and monitor all commodity purchase transactions.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/transactions/create') . '" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Create Transaction
            </a>
        ';

        return view('admin/transactions/transactions_index', $this->data);
    }

    /**
     * Show create transaction form (GET)
     */
    public function create()
    {
        // Get current user ID from session
        $currentUserId = session()->get('user_id');

        if (!$currentUserId) {
            return redirect()->to('login')->with('error', 'Please log in to create transactions.');
        }

        // Get the current user's most recent in-progress mission
        // Use direct database query to avoid soft delete issues
        $db = \Config\Database::connect();
        $currentMission = $db->table('mission')
                            ->select('mission.*,
                                     commodities.commodity_name,
                                     commodities.unit_of_measurement,
                                     locations.location_name,
                                     CONCAT(countries.name, \' > \',
                                            provinces.name, \' > \',
                                            districts.name, \' > \',
                                            llgs.name) as location_hierarchy')
                            ->join('commodities', 'commodities.commodity_id = mission.commodity_id', 'left')
                            ->join('locations', 'locations.id = mission.location_id', 'left')
                            ->join('countries', 'countries.id = locations.country_id', 'left')
                            ->join('provinces', 'provinces.id = locations.province_id', 'left')
                            ->join('districts', 'districts.id = locations.district_id', 'left')
                            ->join('llgs', 'llgs.id = locations.llg_id', 'left')
                            ->where('mission.user_id', $currentUserId)
                            ->where('mission.mission_status', 'in_progress')
                            ->where('mission.deleted_at', null)
                            ->orderBy('mission.mission_date', 'DESC')
                            ->get()
                            ->getRowArray();

        if (!$currentMission) {
            return redirect()->to('admin/transactions')->with('error', 'No active mission found. Please ensure you have an in-progress mission before creating transactions.');
        }

        // Get current user details
        $currentUser = $this->userModel->find($currentUserId);

        // Get active customers (optional field)
        $customers = $this->customerModel->getActiveCustomersForDropdown();

        // Initialize form data with auto-populated values
        $this->data['transaction'] = [
            'user_id' => $currentUserId,
            'mission_id' => $currentMission['id'],
            'commodity_id' => $currentMission['commodity_id'],
            'customer_id' => old('customer_id'),
            'location_id' => $currentMission['location_id'],
            'quantity' => old('quantity'),
            'unit_price' => old('unit_price'),
            'payment_amount' => old('payment_amount'),
            'transaction_date' => old('transaction_date', date('Y-m-d')),
            'remarks' => old('remarks'),
            'status' => old('status', 'completed')
        ];

        // Pass mission and related data for display
        $this->data['current_mission'] = $currentMission;
        $this->data['current_user'] = $currentUser;
        $this->data['customers'] = $customers;

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Transactions', 'url' => base_url('admin/transactions')],
            ['title' => 'Create Transaction', 'url' => base_url('admin/transactions/create')]
        ];

        $this->data['page_title'] = 'Create New Transaction';
        $this->data['page_description'] = 'Record a new commodity purchase transaction.';
        $this->data['page_actions'] = '
            <a href="' . base_url('admin/transactions') . '" class="btn btn-secondary btn-lg">
                <i class="fas fa-arrow-left me-2"></i>Back to Transactions
            </a>
        ';

        return view('admin/transactions/transactions_create', $this->data);
    }

    /**
     * Store new transaction (POST)
     */
    public function store()
    {
        // Add debug logging to see if method is called
        log_message('debug', 'TransactionController::store() method called');
        log_message('debug', 'Request method: ' . $this->request->getMethod());
        log_message('debug', 'POST data: ' . json_encode($this->request->getPost()));

        // Get current user ID from session
        $currentUserId = session()->get('user_id');

        if (!$currentUserId) {
            return redirect()->to('login')->with('error', 'Please log in to create transactions.');
        }

        // Get the current user's most recent in-progress mission
        $currentMission = $this->missionModel->where('user_id', $currentUserId)
                                            ->where('mission_status', 'in_progress')
                                            ->where('deleted_at', null)
                                            ->orderBy('mission_date', 'DESC')
                                            ->first();

        if (!$currentMission) {
            return redirect()->back()->withInput()->with('error', 'No active mission found. Please ensure you have an in-progress mission.');
        }

        // Validation rules - only for user input fields
        $rules = [
            'quantity' => 'required|decimal|greater_than[0]',
            'unit_price' => 'required|decimal|greater_than[0]',
            'transaction_date' => 'required|valid_date'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Calculate payment amount
        $quantity = $this->request->getPost('quantity');
        $unitPrice = $this->request->getPost('unit_price');
        $paymentAmount = round($quantity * $unitPrice, 2);

        $data = [
            'user_id' => $currentUserId,
            'mission_id' => $currentMission['id'],
            'commodity_id' => $currentMission['commodity_id'],
            'customer_id' => $this->request->getPost('customer_id') ?: null,
            'location_id' => $currentMission['location_id'],
            'quantity' => $quantity,
            'unit_price' => $unitPrice,
            'payment_amount' => $paymentAmount,
            'transaction_date' => $this->request->getPost('transaction_date'),
            'remarks' => $this->request->getPost('remarks'),
            'status' => 'completed' // Default to completed as requested
        ];

        // Add created_by and updated_by
        $data['created_by'] = session()->get('user_id');
        $data['updated_by'] = session()->get('user_id');

        // Debug: Log the data being inserted
        log_message('debug', 'Transaction data to insert: ' . json_encode($data));

        try {
            // Check if table exists first
            $db = \Config\Database::connect();
            $tableExists = $db->tableExists('transactions');
            log_message('debug', 'Transactions table exists: ' . ($tableExists ? 'yes' : 'no'));

            if (!$tableExists) {
                return redirect()->back()->withInput()->with('error', 'Transactions table does not exist. Please run database migrations.');
            }

            // Add transaction_code and timestamps for direct insert
            $data['transaction_code'] = 'TXN' . date('YmdHis') . rand(100, 999);
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');

            // Try direct insert using query builder to bypass model validation
            $result = $db->table('transactions')->insert($data);

            // Debug: Log the result
            log_message('debug', 'Transaction creation result: ' . ($result ? 'success' : 'failed'));

            if ($result) {
                $insertId = $db->insertID();
                log_message('debug', 'Insert ID: ' . $insertId);
                return redirect()->to('admin/transactions')->with('success', 'Transaction created successfully.');
            } else {
                log_message('error', 'Failed to insert transaction - no result returned');
                return redirect()->back()->withInput()->with('error', 'Failed to create transaction. Please check your input and try again.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error creating transaction: ' . $e->getMessage());
            log_message('error', 'Transaction data: ' . json_encode($data));
            return redirect()->back()->withInput()->with('error', 'An error occurred while creating the transaction: ' . $e->getMessage());
        }
    }

    /**
     * Show transaction details (GET)
     */
    public function show($id)
    {
        $transaction = $this->transactionModel->getTransactionsWithDetails(1, ['id' => $id]);
        
        if (empty($transaction)) {
            return redirect()->to('admin/transactions')->with('error', 'Transaction not found.');
        }

        $transaction = $transaction[0];

        $this->data['transaction'] = $transaction;

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Transactions', 'url' => base_url('admin/transactions')],
            ['title' => 'Transaction Details', 'url' => base_url('admin/transactions/' . $id)]
        ];

        $this->data['page_title'] = 'Transaction Details';
        $this->data['page_description'] = 'View transaction information and details.';

        return view('admin/transactions/transactions_show', $this->data);
    }

    /**
     * Show edit transaction form (GET)
     */
    public function edit($id)
    {
        $transaction = $this->transactionModel->find($id);

        if (!$transaction) {
            return redirect()->to('admin/transactions')->with('error', 'Transaction not found.');
        }

        // Get form options (same as create)
        $buyers = $this->userModel->where('is_buyer', true)
                                 ->where('status', 'active')
                                 ->orderBy('fullname', 'ASC')
                                 ->findAll();

        $missions = $this->missionModel->select('id, mission_number, mission_name, user_id, commodity_id')
                                     ->where('mission_status !=', 'cancelled')
                                     ->orderBy('mission_date', 'DESC')
                                     ->findAll();

        $commodities = $this->commodityModel->getActiveCommodities();
        $customers = $this->customerModel->getActiveCustomersForDropdown();
        $locations = $this->locationModel->getLocationsForDropdown();

        $this->data['transaction'] = $transaction;
        $this->data['buyers'] = $buyers;
        $this->data['missions'] = $missions;
        $this->data['commodities'] = $commodities;
        $this->data['customers'] = $customers;
        $this->data['locations'] = $locations;

        $this->data['breadcrumbs'] = [
            ['title' => 'Dashboard', 'url' => base_url('dashboard')],
            ['title' => 'Transactions', 'url' => base_url('admin/transactions')],
            ['title' => 'Edit Transaction', 'url' => base_url('admin/transactions/' . $id . '/edit')]
        ];

        $this->data['page_title'] = 'Edit Transaction';
        $this->data['page_description'] = 'Modify transaction information and details.';
        $this->data['page_actions'] = '
            <div class="btn-group">
                <a href="' . base_url('admin/transactions/' . $id) . '" class="btn btn-info btn-lg">
                    <i class="fas fa-eye me-2"></i>View Details
                </a>
                <a href="' . base_url('admin/transactions') . '" class="btn btn-secondary btn-lg">
                    <i class="fas fa-arrow-left me-2"></i>Back to List
                </a>
            </div>
        ';

        return view('admin/transactions/transactions_edit', $this->data);
    }

    /**
     * Update transaction (PUT)
     */
    public function update($id)
    {
        $transaction = $this->transactionModel->find($id);

        if (!$transaction) {
            return redirect()->to('admin/transactions')->with('error', 'Transaction not found.');
        }

        // Validation rules (same as store)
        $rules = [
            'user_id' => 'required|integer',
            'mission_id' => 'required|integer',
            'commodity_id' => 'required|integer',
            'location_id' => 'required|integer',
            'quantity' => 'required|decimal|greater_than[0]',
            'unit_price' => 'required|decimal|greater_than[0]',
            'payment_amount' => 'required|decimal|greater_than[0]',
            'transaction_date' => 'required|valid_date',
            'status' => 'required|in_list[pending,completed,cancelled]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $data = [
            'user_id' => $this->request->getPost('user_id'),
            'mission_id' => $this->request->getPost('mission_id'),
            'commodity_id' => $this->request->getPost('commodity_id'),
            'customer_id' => $this->request->getPost('customer_id') ?: null,
            'location_id' => $this->request->getPost('location_id'),
            'quantity' => $this->request->getPost('quantity'),
            'unit_price' => $this->request->getPost('unit_price'),
            'payment_amount' => $this->request->getPost('payment_amount'),
            'transaction_date' => $this->request->getPost('transaction_date'),
            'remarks' => $this->request->getPost('remarks'),
            'status' => $this->request->getPost('status'),
            'updated_by' => session()->get('user_id')
        ];

        // Same business logic validation as store
        $userId = $data['user_id'];
        $missionId = $data['mission_id'];
        $commodityId = $data['commodity_id'];

        if (!$this->transactionModel->validateMissionAssignment($missionId, $userId)) {
            return redirect()->back()->withInput()->with('error', 'User is not assigned to this mission or mission is not active.');
        }

        if (!$this->transactionModel->validateCommodityMatch($missionId, $commodityId)) {
            return redirect()->back()->withInput()->with('error', 'Commodity does not match the mission assignment.');
        }

        $calculatedAmount = round($data['quantity'] * $data['unit_price'], 2);
        if (abs($calculatedAmount - $data['payment_amount']) > 0.01) {
            return redirect()->back()->withInput()->with('error', 'Payment amount must equal quantity × unit price.');
        }

        try {
            $result = $this->transactionModel->update($id, $data);

            if ($result) {
                return redirect()->to('admin/transactions')->with('success', 'Transaction updated successfully.');
            } else {
                return redirect()->back()->withInput()->with('error', 'Failed to update transaction.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error updating transaction: ' . $e->getMessage());
            return redirect()->back()->withInput()->with('error', 'An error occurred while updating the transaction.');
        }
    }

    /**
     * Delete transaction (DELETE)
     */
    public function delete($id)
    {
        $transaction = $this->transactionModel->find($id);

        if (!$transaction) {
            return redirect()->to('admin/transactions')->with('error', 'Transaction not found.');
        }

        try {
            $result = $this->transactionModel->delete($id);

            if ($result) {
                return redirect()->to('admin/transactions')->with('success', 'Transaction deleted successfully.');
            } else {
                return redirect()->to('admin/transactions')->with('error', 'Failed to delete transaction.');
            }
        } catch (\Exception $e) {
            log_message('error', 'Error deleting transaction: ' . $e->getMessage());
            return redirect()->to('admin/transactions')->with('error', 'An error occurred while deleting the transaction.');
        }
    }
}
