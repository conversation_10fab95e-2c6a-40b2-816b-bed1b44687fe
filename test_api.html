<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test Page</h1>
    <button onclick="testAPI()">Test Districts API</button>
    <div id="result"></div>

    <script>
    async function testAPI() {
        try {
            const response = await fetch('http://localhost/dcbuyer/admin/api/districts/5');
            const data = await response.json();
            
            document.getElementById('result').innerHTML = '<h3>Success!</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        } catch (error) {
            document.getElementById('result').innerHTML = '<h3>Error!</h3><p>' + error.message + '</p>';
        }
    }
    </script>
</body>
</html>