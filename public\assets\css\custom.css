/* Custom CSS for Dakoii Commodity Buyer */

:root {
    --primary-green: #2E7D32;
    --secondary-green: #4CAF50;
    --dark-blue: #1A365D;
    --light-blue: #2D3748;
    --gradient-bg: linear-gradient(135deg, #2E7D32 0%, #1A365D 100%);
    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --border-radius: 15px;
}

/* Utility Classes */
.gradient-bg {
    background: var(--gradient-bg);
}

.text-primary-green {
    color: var(--primary-green) !important;
}

.bg-primary-green {
    background-color: var(--primary-green) !important;
}

.text-dark-blue {
    color: var(--dark-blue) !important;
}

.shadow-custom {
    box-shadow: var(--card-shadow) !important;
}

.border-radius-custom {
    border-radius: var(--border-radius) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 1s ease-in;
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(30px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Utilities */
@media (max-width: 576px) {
    .mobile-center {
        text-align: center !important;
    }
    
    .mobile-full-width {
        width: 100% !important;
    }
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
