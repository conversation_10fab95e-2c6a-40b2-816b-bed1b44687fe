<!DOCTYPE html>
<html>
<head>
    <title>Locations API Test</title>
</head>
<body>
    <h1>Locations API Test Page</h1>
    <button onclick="testProvincesAPI()">Test Provinces API (Country ID: 1)</button>
    <button onclick="testDistrictsAPI()">Test Districts API (Province ID: 5)</button>
    <button onclick="testLlgsAPI()">Test LLGs API (District ID: 7)</button>
    <div id="result"></div>

    <script>
    async function testProvincesAPI() {
        try {
            const response = await fetch('http://localhost/dcbuyer/admin/locations/api/provinces/1');
            const data = await response.json();
            
            document.getElementById('result').innerHTML = '<h3>Provinces API Success!</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        } catch (error) {
            document.getElementById('result').innerHTML = '<h3>Error!</h3><p>' + error.message + '</p>';
        }
    }

    async function testDistrictsAPI() {
        try {
            const response = await fetch('http://localhost/dcbuyer/admin/locations/api/districts/5');
            const data = await response.json();
            
            document.getElementById('result').innerHTML = '<h3>Districts API Success!</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        } catch (error) {
            document.getElementById('result').innerHTML = '<h3>Error!</h3><p>' + error.message + '</p>';
        }
    }

    async function testLlgsAPI() {
        try {
            const response = await fetch('http://localhost/dcbuyer/admin/locations/api/llgs/7');
            const data = await response.json();
            
            document.getElementById('result').innerHTML = '<h3>LLGs API Success!</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        } catch (error) {
            document.getElementById('result').innerHTML = '<h3>Error!</h3><p>' + error.message + '</p>';
        }
    }
    </script>
</body>
</html>